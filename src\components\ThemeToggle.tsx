import { <PERSON>, <PERSON>, <PERSON>pt<PERSON> } from "lucide-react";
import { But<PERSON> } from "@/components/ui/button";
import { useTheme } from "@/components/ThemeProvider";
import { useTranslation } from "react-i18next";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
  DropdownMenuSeparator,
  DropdownMenuShortcut,
} from "@/components/ui/dropdown-menu";
import { cn } from "@/lib/utils";

export function ThemeToggle({ className }: { className?: string }) {
  const { theme, setTheme } = useTheme();
  const { t } = useTranslation();

  return (
    <DropdownMenu>
      <DropdownMenuTrigger asChild>
        <Button
          variant="outline"
          size="icon"
          className={cn(
            "rounded-full w-9 h-9 border-muted-foreground/20 hover:bg-muted transition-colors",
            theme === "dark" && "border-muted-foreground/30",
            className
          )}
          aria-label={t('common.toggleTheme', 'Toggle theme')}
        >
          <Sun className="h-[1.2rem] w-[1.2rem] rotate-0 scale-100 transition-all dark:-rotate-90 dark:scale-0" />
          <Moon className="absolute h-[1.2rem] w-[1.2rem] rotate-90 scale-0 transition-all dark:rotate-0 dark:scale-100" />
          <span className="sr-only">{t('common.toggleTheme', 'Toggle theme')}</span>
        </Button>
      </DropdownMenuTrigger>
      <DropdownMenuContent align="end" className="min-w-[180px]">
        <DropdownMenuItem
          onClick={() => setTheme("light")}
          className={cn("gap-2", theme === "light" && "bg-accent")}
        >
          <Sun className="h-4 w-4" />
          <span>{t('common.lightTheme', 'Light')}</span>
          {theme === "light" && (
            <DropdownMenuShortcut>✓</DropdownMenuShortcut>
          )}
        </DropdownMenuItem>
        <DropdownMenuItem
          onClick={() => setTheme("dark")}
          className={cn("gap-2", theme === "dark" && "bg-accent")}
        >
          <Moon className="h-4 w-4" />
          <span>{t('common.darkTheme', 'Dark')}</span>
          {theme === "dark" && (
            <DropdownMenuShortcut>✓</DropdownMenuShortcut>
          )}
        </DropdownMenuItem>
        <DropdownMenuSeparator />
        <DropdownMenuItem
          onClick={() => setTheme("system")}
          className={cn("gap-2", theme === "system" && "bg-accent")}
        >
          <Laptop className="h-4 w-4" />
          <span>{t('common.systemTheme', 'System')}</span>
          {theme === "system" && (
            <DropdownMenuShortcut>✓</DropdownMenuShortcut>
          )}
        </DropdownMenuItem>
      </DropdownMenuContent>
    </DropdownMenu>
  );
}
