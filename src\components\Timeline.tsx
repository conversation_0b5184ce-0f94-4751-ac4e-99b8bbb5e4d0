import { cn } from "@/lib/utils";
import { useLanguage } from "@/i18n/LanguageProvider";

interface TimelineItemProps {
  year: string;
  title: string;
  description: string;
  isLast?: boolean;
}

function TimelineItem({ year, title, description, isLast = false }: TimelineItemProps) {
  const { direction } = useLanguage();
  const isRTL = direction === 'rtl';

  return (
    <div className={cn(
      "relative pb-8 mb-2 md:mb-4",
      isRTL ? "pr-8 md:pr-10" : "pl-8 md:pl-10"
    )}>
      <div className={cn(
        "absolute top-0 h-full flex items-center justify-center",
        isRTL ? "right-0" : "left-0"
      )}>
        <div className="h-full w-px bg-border dark:bg-border/50"></div>
      </div>
      <div className={cn(
        "absolute top-1 w-5 h-5 md:w-6 md:h-6 rounded-full bg-primary flex items-center justify-center",
        isRTL ? "right-0" : "left-0"
      )}>
        <div className="w-1.5 h-1.5 md:w-2 md:h-2 rounded-full bg-white"></div>
      </div>
      <div className="pt-1">
        <span className="block text-sm font-medium text-muted-foreground mb-1">{year}</span>
        <h3 className="text-lg md:text-xl font-bold mb-2">{title}</h3>
        <p className="text-sm md:text-base text-muted-foreground">{description}</p>
      </div>
      {!isLast && <div className={cn(
        "absolute bottom-0 h-8 w-px bg-border dark:bg-border/50",
        isRTL ? "right-0" : "left-0"
      )}></div>}
    </div>
  );
}

interface TimelineProps {
  items: Array<{
    id?: string;
    year: string;
    title: string;
    description: string;
  }>;
  className?: string;
}

export function Timeline({ items, className }: TimelineProps) {
  const { direction } = useLanguage();

  // Ensure items is an array
  const timelineItems = Array.isArray(items) ? items : [];

  return (
    <div className={cn(
      "relative px-4 sm:px-0",
      className
    )}>
      {timelineItems.map((item, index) => (
        <TimelineItem
          key={item.id || `timeline-item-${item.year}-${index}`}
          year={item.year}
          title={item.title}
          description={item.description}
          isLast={index === timelineItems.length - 1}
        />
      ))}
    </div>
  );
}
