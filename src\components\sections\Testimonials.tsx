
import { Star, Quote } from "lucide-react";
import { Badge } from "@/components/ui/badge";
import { useTranslation } from "react-i18next";
import { cn } from "@/lib/utils";
import { useLanguage } from "@/i18n/LanguageProvider";

const testimonials = [
  {
    id: "testimonial-1",
    content: "CodeSafir transformed our outdated website into a modern, responsive platform that perfectly represents our brand. Their attention to detail and technical expertise exceeded our expectations.",
    author: "<PERSON>",
    role: "Marketing Director at Elevate Retail",
    rating: 5,
    highlight: "40% increase in leads",
    avatar: "https://randomuser.me/api/portraits/women/32.jpg"
  },
  {
    id: "testimonial-2",
    content: "Working with CodeSafir was a game-changer for our business. They built our e-commerce platform from scratch, and we've seen a 40% increase in online sales since launch. Their team is responsive, professional, and truly cares about our success.",
    author: "<PERSON>",
    role: "CEO at Artisan Goods",
    rating: 5,
    highlight: "40% increase in sales",
    avatar: "https://randomuser.me/api/portraits/men/46.jpg"
  },
  {
    id: "testimonial-3",
    content: "The web application CodeSafir developed for our healthcare practice has streamlined our patient management process significantly. Their understanding of both technical requirements and user experience design is impressive.",
    author: "Dr. Amelia Rodriguez",
    role: "Medical Director at Wellness Group",
    rating: 5,
    highlight: "Streamlined operations",
    avatar: "https://randomuser.me/api/portraits/women/65.jpg"
  },
];

const Testimonials = () => {
  const { t } = useTranslation();
  const { direction } = useLanguage();

  return (
    <section className="py-16 md:py-24 bg-muted/30 dark:bg-muted/10 relative">
      {/* Background decorations */}
      <div className="absolute top-1/4 left-0 w-72 h-72 bg-primary/5 rounded-full blur-3xl -z-10"></div>
      <div className="absolute bottom-1/4 right-0 w-80 h-80 bg-secondary/5 rounded-full blur-3xl -z-10"></div>

      <div className="container">
        <div className="text-center max-w-3xl mx-auto mb-16">
          <span className="inline-block text-sm font-medium text-secondary bg-secondary/10 dark:bg-secondary/20 rounded-full px-3 py-1.5 mb-4">
            {t('testimonials.title', 'Client Testimonials')}
          </span>
          <h2 className="text-3xl md:text-4xl font-bold mb-6 text-foreground">
            {t('testimonials.heading', 'What Our Clients Say')}
          </h2>
          <p className="text-lg text-muted-foreground">
            {t('testimonials.subtitle', "Don't just take our word for it. Here's what our clients have to say about our web development services.")}
          </p>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
          {testimonials.map((testimonial, index) => (
            <div
              key={testimonial.id}
              className="bg-card p-8 rounded-xl border border-border shadow-sm hover:shadow-md transition-all duration-300 flex flex-col animate-fade-in"
              style={{ animationDelay: `${index * 100}ms` }}
            >
              <div className="flex justify-between items-start mb-6">
                <Badge variant="outline" className="bg-primary/10 text-primary self-start">
                  {testimonial.highlight}
                </Badge>
                <Quote className="h-8 w-8 text-primary/20" />
              </div>

              <div className="flex mb-4">
                {Array.from({ length: testimonial.rating }).map((_, i) => (
                  <Star key={`${testimonial.id}-star-${i}`} className="h-5 w-5 fill-yellow-400 text-yellow-400" />
                ))}
              </div>

              <blockquote className="text-foreground/90 mb-6 flex-grow italic">"{testimonial.content}"</blockquote>

              <div className={cn(
                "flex items-center pt-4 border-t border-border",
                direction === 'rtl' ? 'flex-row-reverse' : 'flex-row'
              )}>
                {testimonial.avatar ? (
                  <img
                    src={testimonial.avatar}
                    alt={testimonial.author}
                    className="w-12 h-12 rounded-full object-cover border-2 border-background"
                  />
                ) : (
                  <div className="w-12 h-12 rounded-full bg-gradient-to-br from-primary/70 to-primary flex items-center justify-center text-white font-semibold">
                    {testimonial.author.charAt(0)}
                  </div>
                )}
                <div className={direction === 'rtl' ? 'mr-3' : 'ml-3'}>
                  <div className="font-medium">{testimonial.author}</div>
                  <div className="text-sm text-muted-foreground">{testimonial.role}</div>
                </div>
              </div>
            </div>
          ))}
        </div>
      </div>
    </section>
  );
};

export default Testimonials;
