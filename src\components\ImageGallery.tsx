import React, { useState } from "react";
import { X, ChevronLeft, ChevronRight } from "lucide-react";
import { cn } from "@/lib/utils";
import { useLanguage } from "@/i18n/LanguageProvider";
import { Button } from "@/components/ui/button";
import { Dialog, DialogContent } from "@/components/ui/dialog";

interface ImageGalleryProps {
  images: string[];
  alt: string;
}

export function ImageGallery({ images, alt }: ImageGalleryProps) {
  const [open, setOpen] = useState(false);
  const [currentIndex, setCurrentIndex] = useState(0);
  const { direction } = useLanguage();

  const handlePrevious = (e: React.MouseEvent) => {
    e.stopPropagation();
    setCurrentIndex((prev) => (prev === 0 ? images.length - 1 : prev - 1));
  };

  const handleNext = (e: React.MouseEvent) => {
    e.stopPropagation();
    setCurrentIndex((prev) => (prev === images.length - 1 ? 0 : prev + 1));
  };

  const handleKeyDown = (e: React.KeyboardEvent) => {
    if (e.key === "ArrowLeft") {
      setCurrentIndex((prev) => (prev === 0 ? images.length - 1 : prev - 1));
    } else if (e.key === "ArrowRight") {
      setCurrentIndex((prev) => (prev === images.length - 1 ? 0 : prev + 1));
    } else if (e.key === "Escape") {
      setOpen(false);
    }
  };

  return (
    <>
      <div className="grid grid-cols-1 md:grid-cols-2 gap-6 mb-6">
        {images.slice(0, 2).map((image, index) => (
          <div
            key={index}
            className="rounded-lg overflow-hidden border border-border cursor-pointer hover:opacity-90 transition-opacity"
            onClick={() => {
              setCurrentIndex(index);
              setOpen(true);
            }}
          >
            <img
              src={image}
              alt={`${alt} - Image ${index + 1}`}
              className="w-full h-auto object-cover"
            />
          </div>
        ))}
      </div>
      {images.length > 2 && (
        <div
          className="rounded-lg overflow-hidden border border-border cursor-pointer hover:opacity-90 transition-opacity mb-6"
          onClick={() => {
            setCurrentIndex(2);
            setOpen(true);
          }}
        >
          <img
            src={images[2]}
            alt={`${alt} - Image 3`}
            className="w-full h-auto object-cover"
          />
        </div>
      )}

      <Dialog open={open} onOpenChange={setOpen}>
        <DialogContent
          className="max-w-5xl w-[95vw] p-0 bg-transparent border-none shadow-none"
          onKeyDown={handleKeyDown}
        >
          <div className="relative bg-background/95 backdrop-blur-sm rounded-lg overflow-hidden">
            <Button
              variant="ghost"
              size="icon"
              className="absolute top-2 right-2 z-50"
              onClick={() => setOpen(false)}
            >
              <X className="h-5 w-5" />
            </Button>

            <div className="relative aspect-video">
              <img
                src={images[currentIndex]}
                alt={`${alt} - Image ${currentIndex + 1}`}
                className="w-full h-full object-contain"
              />

              <Button
                variant="ghost"
                size="icon"
                className={cn(
                  "absolute top-1/2 -translate-y-1/2 bg-background/50 hover:bg-background/70 backdrop-blur-sm",
                  direction === 'rtl' ? "right-2" : "left-2"
                )}
                onClick={handlePrevious}
              >
                <ChevronLeft className={cn("h-6 w-6", direction === 'rtl' && "rotate-180")} />
              </Button>

              <Button
                variant="ghost"
                size="icon"
                className={cn(
                  "absolute top-1/2 -translate-y-1/2 bg-background/50 hover:bg-background/70 backdrop-blur-sm",
                  direction === 'rtl' ? "left-2" : "right-2"
                )}
                onClick={handleNext}
              >
                <ChevronRight className={cn("h-6 w-6", direction === 'rtl' && "rotate-180")} />
              </Button>
            </div>

            <div className="p-4 flex justify-center">
              <div className={cn(
                "flex",
                direction === 'rtl' ? 'space-x-reverse space-x-2' : 'space-x-2'
              )}>
                {images.map((_, index) => (
                  <button
                    key={index}
                    className={cn(
                      "w-2 h-2 rounded-full transition-colors",
                      index === currentIndex ? "bg-primary" : "bg-muted-foreground/30 hover:bg-muted-foreground/50"
                    )}
                    onClick={() => setCurrentIndex(index)}
                  />
                ))}
              </div>
            </div>
          </div>
        </DialogContent>
      </Dialog>
    </>
  );
}
