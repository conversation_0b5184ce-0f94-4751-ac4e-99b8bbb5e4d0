import { <PERSON> } from "react-router-dom";
import { useTranslation } from "react-i18next";
import { Button } from "@/components/ui/button";
import { CaseStudy } from "@/types/services";
import { memo } from "react";

interface CaseStudiesSectionProps {
    caseStudies: CaseStudy[];
}

const CaseStudiesSection = memo(function CaseStudiesSection({ caseStudies }: CaseStudiesSectionProps) {
    const { t } = useTranslation();

    return (
        <section className="py-16 md:py-24 bg-muted/30 dark:bg-muted/10">
            <div className="container">
                <div className="text-center max-w-3xl mx-auto mb-16">
                    <span className="inline-block text-sm font-medium text-secondary bg-secondary/10 dark:bg-secondary/20 rounded-full px-3 py-1 mb-4">
                        {t('home.portfolio.title')}
                    </span>
                    <h2 className="text-3xl md:text-4xl font-bold mb-6 text-foreground">
                        {t('home.portfolio.subtitle')}
                    </h2>
                    <p className="text-lg text-muted-foreground">
                        {t('services.caseStudies.subtitle')}
                    </p>
                </div>

                <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-2 gap-6 md:gap-8">
                    {caseStudies.map((study) => (
                        <div key={study.id} className="bg-card rounded-xl border border-border overflow-hidden shadow-md hover:shadow-lg transition-shadow">
                            <div className="aspect-video relative">
                                <img
                                    src={study.image}
                                    alt={study.title}
                                    className="object-cover w-full h-full"
                                    loading="lazy"
                                />
                            </div>
                            <div className="p-4 sm:p-6">
                                <h3 className="text-xl sm:text-2xl font-bold mb-2">{study.title}</h3>
                                <p className="text-secondary font-medium mb-2 sm:mb-3">{study.client}</p>
                                <p className="text-muted-foreground mb-3 sm:mb-4 text-sm sm:text-base">
                                    {study.description}
                                </p>
                                <div className="flex flex-wrap gap-2 mb-4 sm:mb-6">
                                    {study.technologies.map((tech) => (
                                        <span
                                            key={`${study.id}-${tech}`}
                                            className="px-2 sm:px-3 py-1 bg-secondary/10 text-secondary rounded-full text-xs sm:text-sm"
                                        >
                                            {tech}
                                        </span>
                                    ))}
                                </div>
                                <Button variant="outline" className="w-full" asChild>
                                    <Link to={`/portfolio/${study.id}`}>
                                        {t('home.portfolio.viewProject')}
                                    </Link>
                                </Button>
                            </div>
                        </div>
                    ))}
                </div>

                <div className="text-center mt-12">
                    <Button variant="outline" asChild>
                        <Link to="/portfolio">{t('common.viewAll')}</Link>
                    </Button>
                </div>
            </div>
        </section>
    );
});

export default CaseStudiesSection;