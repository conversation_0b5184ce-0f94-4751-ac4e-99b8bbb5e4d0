# CodeSafir Sanity Studio

This is the Sanity Studio for managing content on the CodeSafir website. It provides a headless CMS for the portfolio and blog sections.

## Getting Started

1. First, install dependencies:

```bash
npm install
```

2. To start the development server:

```bash
npm run dev
```

This will start the Sanity Studio development server at [http://localhost:3333](http://localhost:3333).

## Content Models

The following content models have been defined:

### Portfolio Projects

Manage your portfolio projects with the following fields:
- Project Title
- Client Name
- Website URL
- Project Images
- Technologies Used
- Description
- Detailed Description

### Blog Posts

Manage your blog content with the following features:
- Blog Title
- Author
- Categories
- Featured Image
- Rich Text Content with Images
- Code Snippets with Syntax Highlighting

### Authors

Manage blog post authors with:
- Name
- Profile Image
- Bio

### Categories

Organize blog posts with categories:
- Title
- Description

## Deployment

To deploy your Sanity Studio to production:

```bash
npm run build
npm run deploy
```

## Connecting to Your Frontend

To fetch content in your frontend application:

1. Install the Sanity client in your frontend project:

```bash
npm install @sanity/client
```

2. Create a client configuration:

```typescript
import { createClient } from '@sanity/client'

export const client = createClient({
  projectId: '4eq3rxcw',
  dataset: 'production',
  useCdn: true, // set to `false` to bypass the edge cache
  apiVersion: '2023-05-03', // use current date (YYYY-MM-DD) to target the latest API version
})
```

3. Query your content using GROQ:

```typescript
// Example: Fetch all portfolio projects
const portfolioProjects = await client.fetch(`*[_type == "portfolio"] | order(publishedAt desc)`)

// Example: Fetch all blog posts with their categories
const blogPosts = await client.fetch(`
  *[_type == "blog"] {
    ...,
    "categories": categories[]->title
  } | order(publishedAt desc)
`)
```

## Learn More

- [Sanity Documentation](https://www.sanity.io/docs)
- [GROQ Query Language](https://www.sanity.io/docs/groq)
- [Sanity Content Lake](https://www.sanity.io/docs/datastore)
