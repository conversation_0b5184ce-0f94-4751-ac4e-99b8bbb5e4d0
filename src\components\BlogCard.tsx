import { Calendar, User, ArrowRight } from "lucide-react";
import { <PERSON> } from "react-router-dom";
import { useTranslation } from "react-i18next";
import { useLanguage } from "@/i18n/LanguageProvider";
import { cn } from "@/lib/utils";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { BlogPost } from "@/data/blog-posts";

interface BlogCardProps {
  post: BlogPost;
  compact?: boolean;
}

export function BlogCard({ post, compact = false }: BlogCardProps) {
  const { t } = useTranslation();
  const { direction } = useLanguage();

  // Format date
  const formatDate = (dateString: string) => {
    const date = new Date(dateString);
    return new Intl.DateTimeFormat('en-US', { 
      year: 'numeric', 
      month: 'long', 
      day: 'numeric' 
    }).format(date);
  };

  return (
    <div className="bg-card rounded-xl overflow-hidden border border-border hover:shadow-lg transition-all duration-300 group h-full flex flex-col">
      <div className="aspect-[16/9] relative">
        <img
          src={post.image}
          alt={post.title}
          className="object-cover w-full h-full group-hover:scale-105 transition-transform duration-300"
        />
        <div className="absolute top-4 right-4">
          <Badge variant="secondary" className="bg-secondary/80 hover:bg-secondary/90">
            {post.category}
          </Badge>
        </div>
      </div>
      <div className="p-6 flex flex-col flex-grow">
        <div className="flex items-center text-sm text-muted-foreground mb-3">
          <div className="flex items-center">
            <Calendar className="h-4 w-4 mr-1" />
            <span>{formatDate(post.date)}</span>
          </div>
          <div className="mx-2">•</div>
          <div className="flex items-center">
            <User className="h-4 w-4 mr-1" />
            <span>{post.author}</span>
          </div>
        </div>
        
        <h3 className="text-xl font-bold mb-2 text-foreground">
          {post.title}
        </h3>
        
        {!compact && (
          <p className="text-muted-foreground mb-4 line-clamp-2 flex-grow">
            {post.excerpt}
          </p>
        )}
        
        <Button
          variant="link"
          className={cn(
            "p-0 h-auto text-primary mt-auto",
            direction === 'rtl' ? 'flex flex-row-reverse' : 'flex'
          )}
          asChild
        >
          <Link to={`/blog/${post.id}`}>
            {t('common.readMore')}
            <ArrowRight className={cn("h-4 w-4", direction === 'rtl' ? 'mr-1 rotate-180' : 'ml-1')} />
          </Link>
        </Button>
      </div>
    </div>
  );
}
