import { ArrowRight, CheckCircle } from "lucide-react";
import { Link } from "react-router-dom";
import { useTranslation } from "react-i18next";
import { Button } from "@/components/ui/button";
import { Service } from "@/types/services";
import { useLanguage } from "@/i18n/LanguageProvider";
import { cn } from "@/lib/utils";
import { memo } from "react";

interface ServiceDetailsProps {
    service: Service;
}

export const ServiceDetails = memo(function ServiceDetails({ service }: ServiceDetailsProps) {
    const { t } = useTranslation();
    const { direction } = useLanguage();

    return (
        <section className="py-16 md:py-24">
            <div className="container">
                <div className="max-w-4xl mx-auto">
                    <div className="flex items-center mb-8">
                        <Link to="/services" className="text-muted-foreground hover:text-foreground flex items-center">
                            <ArrowRight className={cn(
                                "h-4 w-4 rotate-180 mr-2",
                                direction === 'rtl' ? 'rotate-0 ml-2' : ''
                            )} />
                            {t('services.hero.title')}
                        </Link>
                    </div>

                    <div className="mb-8">
                        <div className="p-4 bg-secondary/10 dark:bg-secondary/20 rounded-xl mb-6 w-16 h-16 flex items-center justify-center">
                            <service.icon className="h-8 w-8 text-secondary" />
                        </div>
                        <h1 className="text-4xl md:text-5xl font-bold mb-6 text-foreground">
                            {service.title}
                        </h1>
                        <p className="text-xl text-muted-foreground">
                            {service.description}
                        </p>
                    </div>

                    <div className="bg-card rounded-xl border border-border p-8 mb-12 shadow-md">
                        <h2 className="text-2xl font-bold mb-6">{t('common.features')}</h2>
                        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                            {service.features.map((feature, index) => (
                                <div key={`${service.id}-feature-${index}`} className="flex items-start p-3 rounded-lg hover:bg-muted/50 transition-colors">
                                    <div className={cn(
                                        "mt-1 flex-shrink-0 text-primary",
                                        direction === 'rtl' ? 'ml-3' : 'mr-3'
                                    )}>
                                        <CheckCircle className="h-5 w-5" />
                                    </div>
                                    <span className="text-foreground">{feature}</span>
                                </div>
                            ))}
                        </div>
                    </div>

                    <div className="mt-12">
                        <Button size="lg" asChild>
                            <Link to="/contact">{t('common.contactUs')}</Link>
                        </Button>
                    </div>
                </div>
            </div>
        </section>
    );
});