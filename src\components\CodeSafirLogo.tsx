import { cn } from "@/lib/utils";

interface CodeSafirLogoProps {
  className?: string;
  size?: "sm" | "md" | "lg";
}

export function CodeSafirLogo({ className, size = "md" }: CodeSafirLogoProps) {
  const dimensions = {
    sm: { height: 120, width: 120 },
    md: { height: 180, width: 180 },
    lg: { height: 240, width: 240 },
  };

  return (
    <div
      className={cn("relative", className)}
      style={{
        width: dimensions[size].width,
        height: dimensions[size].height
      }}
    >
      <img
        src="/logo.png"
        alt="CodeSafir Logo"
        width={dimensions[size].width}
        height={dimensions[size].height}
        loading="lazy"
        decoding="async"
        className={cn("h-full w-full object-contain dark:hidden")}
      />
      <img
        src="/logo-dark.png"
        alt="CodeSafir Logo"
        width={dimensions[size].width}
        height={dimensions[size].height}
        loading="lazy"
        decoding="async"
        className={cn("hidden h-full w-full object-contain dark:block")}
      />
    </div>
  );
}
