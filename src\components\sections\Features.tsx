
import {
  Code,
  Smartphone,
  ShoppingCart,
  Palette,
  Globe,
  Rocket,
  Database,
  Search
} from 'lucide-react';
import { useTranslation } from 'react-i18next';
import { useLanguage } from '@/i18n/LanguageProvider';
import { cn } from '@/lib/utils';
import { Link } from 'react-router-dom';

const features = [
  {
    id: 'custom-web-development',
    icon: Code,
    title: 'Custom Web Development',
    description: 'Bespoke websites and web applications built with modern frameworks like React, Vue, and Angular to meet your specific business needs.'
  },
  {
    id: 'responsive-design',
    icon: Smartphone,
    title: 'Responsive Design',
    description: 'Mobile-first websites that look and function perfectly on all devices, from smartphones to desktops, ensuring a seamless user experience.'
  },
  {
    id: 'ecommerce-solutions',
    icon: ShoppingCart,
    title: 'E-commerce Solutions',
    description: 'Custom online stores with secure payment gateways, inventory management, and seamless checkout experiences to boost your sales.'
  },
  {
    id: 'ui-ux-design',
    icon: Palette,
    title: 'UI/UX Design',
    description: 'User-centered design that combines aesthetics with functionality, creating intuitive interfaces that delight your users.'
  },
  {
    id: 'web-app-development',
    icon: Globe,
    title: 'Web App Development',
    description: 'Progressive Web Apps (PWAs) and Single Page Applications (SPAs) that offer native-like experiences with offline capabilities.'
  },
  {
    id: 'performance-optimization',
    icon: Rocket,
    title: 'Performance Optimization',
    description: 'Speed up your website with advanced optimization techniques, improving load times, user experience, and search engine rankings.'
  },
  {
    id: 'cms-development',
    icon: Database,
    title: 'CMS Development',
    description: 'Custom content management systems or integration with platforms like WordPress, Strapi, or Contentful for easy content updates.'
  },
  {
    id: 'seo-services',
    icon: Search,
    title: 'SEO Services',
    description: 'Search engine optimization to improve your website\'s visibility, drive organic traffic, and increase conversions.'
  }
];

const Features = () => {
  const { t } = useTranslation();
  const { direction } = useLanguage();

  return (
    <section id="features" className="py-16 bg-muted/30 dark:bg-muted/10 md:py-24 relative">
      {/* Background decorations */}
      <div className="absolute top-1/4 right-0 w-72 h-72 bg-primary/5 rounded-full blur-3xl -z-10"></div>
      <div className="absolute bottom-1/4 left-0 w-80 h-80 bg-secondary/5 rounded-full blur-3xl -z-10"></div>

      <div className="container">
        <div className="text-center max-w-3xl mx-auto mb-16">
          <div className="inline-flex items-center gap-2 text-sm font-medium text-secondary bg-secondary/10 dark:bg-secondary/20 rounded-full px-3 py-1.5 mb-4">
            <span>{t('features.subtitle', 'Our Services')}</span>
          </div>

          <h2 className="text-3xl md:text-4xl font-bold mb-6 text-foreground">
            {t('features.title', 'Comprehensive Web Development Services')}
          </h2>

          <p className="text-lg text-muted-foreground max-w-2xl mx-auto">
            {t('features.description', 'We offer end-to-end web development solutions to help businesses establish a powerful online presence with beautiful, functional, and high-performing websites.')}
          </p>
        </div>

        <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-6 md:gap-8">
          {features.map((feature, index) => {
            const Icon = feature.icon;
            return (
              <div
                key={feature.id}
                className={cn(
                  "bg-card p-6 md:p-8 rounded-xl shadow-sm border border-border hover:shadow-lg transition-all duration-300 hover:-translate-y-1 group",
                  "animate-fade-in"
                )}
                style={{ animationDelay: `${index * 100}ms` }}
              >
                <div className="p-3 bg-secondary/10 dark:bg-secondary/20 rounded-xl inline-flex items-center justify-center mb-5 group-hover:bg-secondary/20 dark:group-hover:bg-secondary/30 transition-colors">
                  <Icon className="h-6 w-6 text-secondary" />
                </div>

                <h3 className="font-semibold text-xl mb-3 text-foreground group-hover:text-primary transition-colors">
                  {t(`features.${feature.id}.title`, feature.title)}
                </h3>

                <p className="text-muted-foreground">
                  {t(`features.${feature.id}.description`, feature.description)}
                </p>
              </div>
            );
          })}
        </div>

        <div className="mt-16 text-center">
          <Link
            to="/services"
            className={cn(
              "inline-flex items-center text-primary hover:text-primary/80 font-medium transition-colors",
              direction === 'rtl' ? 'flex-row-reverse' : ''
            )}
          >
            <span>{t('features.viewAllServices', 'View all our services')}</span>
            <svg
              xmlns="http://www.w3.org/2000/svg"
              className={cn("h-4 w-4", direction === 'rtl' ? 'mr-2' : 'ml-2')}
              fill="none"
              viewBox="0 0 24 24"
              stroke="currentColor"
            >
              <path
                strokeLinecap="round"
                strokeLinejoin="round"
                strokeWidth={2}
                d={direction === 'rtl' ? "M19 12H5" : "M5 12h14"}
              />
              <path
                strokeLinecap="round"
                strokeLinejoin="round"
                strokeWidth={2}
                d={direction === 'rtl' ? "M12 19l-7-7 7-7" : "M12 5l7 7-7 7"}
              />
            </svg>
          </Link>
        </div>
      </div>
    </section>
  );
};

export default Features;
