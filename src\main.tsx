import { createRoot } from 'react-dom/client'
import { Suspense } from 'react'
import App from './App.tsx'
import './index.css'
import './i18n/i18n'

// Add error handling for initial render
const root = document.getElementById("root");
if (!root) {
    throw new Error("Root element not found");
}

createRoot(root).render(
    <Suspense fallback={<div>Loading application...</div>}>
        <App />
    </Suspense>
);
