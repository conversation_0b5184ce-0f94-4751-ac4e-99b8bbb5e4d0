import { LucideIcon } from "lucide-react";
import { <PERSON> } from "react-router-dom";
import { useTranslation } from "react-i18next";
import { cn } from "@/lib/utils";
import { Button } from "@/components/ui/button";
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from "@/components/ui/card";
import { useLanguage } from "@/i18n/LanguageProvider";

interface ServiceCardProps {
  icon: LucideIcon;
  title: string;
  description: string;
  features: string[];
  className?: string;
  id?: string;
}

export function ServiceCard({ icon: Icon, title, description, features, className, id }: ServiceCardProps) {
  const { t } = useTranslation();
  const { direction } = useLanguage();

  // Only show first 4 features in the card to keep it clean
  const displayFeatures = features.slice(0, 4);
  const hasMoreFeatures = features.length > 4;

  return (
    <Card className={cn("flex flex-col h-full group hover:shadow-lg transition-all duration-300", className)}>
      <CardHeader className="pb-3 sm:pb-4">
        <div className="p-3 sm:p-4 bg-secondary/10 dark:bg-secondary/20 rounded-lg sm:rounded-xl mb-3 sm:mb-4 w-12 h-12 sm:w-14 sm:h-14 flex items-center justify-center group-hover:bg-secondary/20 transition-colors">
          <Icon className="h-6 w-6 sm:h-7 sm:w-7 text-secondary" />
        </div>
        <CardTitle className="text-xl sm:text-2xl mb-1 sm:mb-2">{title}</CardTitle>
        <CardDescription className="text-sm sm:text-base">{description}</CardDescription>
      </CardHeader>
      <CardContent className="flex-grow pb-3 sm:pb-4">
        <div className="border-t border-border pt-3 sm:pt-4 mb-3 sm:mb-4">
          <h4 className="text-xs sm:text-sm font-medium text-muted-foreground mb-2 sm:mb-3">{t('common.features')}:</h4>
          <ul className="space-y-2 sm:space-y-3">
            {displayFeatures.map((feature, index) => (
              <li key={`${id || title}-feature-${index}`} className="flex items-start">
                <svg
                  className={cn(
                    "h-4 w-4 sm:h-5 sm:w-5 text-secondary flex-shrink-0 mt-0.5",
                    direction === 'rtl' ? 'ml-1.5 sm:ml-2' : 'mr-1.5 sm:mr-2'
                  )}
                  xmlns="http://www.w3.org/2000/svg"
                  viewBox="0 0 20 20"
                  fill="currentColor"
                >
                  <path
                    fillRule="evenodd"
                    d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z"
                    clipRule="evenodd"
                  />
                </svg>
                <span className="text-sm sm:text-base text-foreground">{feature}</span>
              </li>
            ))}
            {hasMoreFeatures && (
              <li className="text-xs sm:text-sm text-muted-foreground italic">
                {t('common.andMore', 'And more...')}
              </li>
            )}
          </ul>
        </div>
      </CardContent>
      <CardFooter>
        {id ? (
          <Button className="w-full text-sm sm:text-base group-hover:bg-primary/90 transition-colors" asChild>
            <Link to={`/services/${id}`}>{t('common.learnMore')}</Link>
          </Button>
        ) : (
          <Button className="w-full text-sm sm:text-base group-hover:bg-primary/90 transition-colors">{t('common.learnMore')}</Button>
        )}
      </CardFooter>
    </Card>
  );
}
