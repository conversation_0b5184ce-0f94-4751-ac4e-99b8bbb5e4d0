import { LucideIcon } from "lucide-react";

export interface ServiceFeature {
    title: string;
    description: string;
}

export interface Service {
    id: string;
    icon: LucideIcon;
    title: string;
    description: string;
    features: string[];
}

export interface CaseStudy {
    id: string;
    title: string;
    client: string;
    description: string;
    technologies: string[];
    image: string;
}

export interface ProcessStep {
    id: string;
    number: string;
    icon: LucideIcon;
    titleKey: string;
    descriptionKey: string;
}