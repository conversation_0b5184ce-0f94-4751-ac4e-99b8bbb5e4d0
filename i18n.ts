import { definePlugin } from 'sanity';

export const i18n = definePlugin({
  name: 'i18n',
  document: {
    // Internationalization setup
    languages: [
      { id: 'en', title: 'English' },
      { id: 'ar', title: 'Arabic' },
    ],
    // Base language used as source
    base: 'en',
    // Field to use as reference in the document
    reference: 'i18n',
    // Fields that should be translated
    translatable: ['title', 'description', 'excerpt', 'body'],
    // Fields that shouldn't be translated
    untranslatable: ['image', 'slug', 'date', 'author'],
  },
});
