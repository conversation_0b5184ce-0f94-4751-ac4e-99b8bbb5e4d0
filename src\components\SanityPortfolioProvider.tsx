import { createContext, useContext, useState, useEffect, ReactNode, Suspense } from 'react';
import { Loader2 } from 'lucide-react';
import { ErrorBoundary } from '@/components/ui/error-boundary';
import { getPortfolioItems } from '@/lib/sanity';
import { PortfolioItem } from '@/data/portfolio';
import { LoadingState } from '@/components/ui/loading-state';
import { Alert, AlertTitle, AlertDescription } from '@/components/ui/alert';

interface SanityPortfolioContextType {
  portfolioItems: PortfolioItem[];
  loading: boolean;
  error: Error | null;
}

const SanityPortfolioContext = createContext<SanityPortfolioContextType>({
  portfolioItems: [],
  loading: false,
  error: null,
});

export const useSanityPortfolio = () => useContext(SanityPortfolioContext);

interface SanityPortfolioProviderProps {
  children: ReactNode;
  fallbackData?: PortfolioItem[]; // Local data to use while loading
}

export function SanityPortfolioProvider({
  children,
  fallbackData = []
}: SanityPortfolioProviderProps) {
  const [portfolioItems, setPortfolioItems] = useState<PortfolioItem[]>(fallbackData);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<Error | null>(null);
  const [isStale, setIsStale] = useState(false);

  useEffect(() => {
    let isMounted = true;

    const fetchPortfolioItems = async () => {
      try {
        // Only show loading state if we don't have any data
        if (portfolioItems.length === 0) {
          setLoading(true);
        } else {
          // Mark existing data as stale while we fetch new data
          setIsStale(true);
        }

        const sanityItems = await getPortfolioItems();

        if (isMounted) {
          // Merge Sanity data with fallback data, prioritizing Sanity data
          const mergedItems = [...sanityItems, ...fallbackData.filter(fallbackItem =>
            !sanityItems.some(sanityItem =>
              sanityItem._id === fallbackItem._id ||
              sanityItem._id === fallbackItem.id ||
              sanityItem.slug?.current === fallbackItem.id
            )
          )];

          setPortfolioItems(mergedItems);
          setError(null);
        }
      } catch (err) {
        console.error('Error fetching portfolio items:', err);
        if (isMounted) {
          // If Sanity fails, use fallback data
          setPortfolioItems(fallbackData);
          setError(err instanceof Error ? err : new Error('Failed to fetch portfolio items'));
        }
      } finally {
        if (isMounted) {
          setLoading(false);
          setIsStale(false);
        }
      }
    };

    fetchPortfolioItems();

    // Cleanup subscription
    return () => {
      isMounted = false;
    };
  }, [fallbackData, portfolioItems.length]);

  return (
    <ErrorBoundary>
      <SanityPortfolioContext.Provider value={{ portfolioItems, loading, error }}>
        <Suspense fallback={
          <LoadingState
            message="Loading portfolio items..."
            size="lg"
          />
        }>
          {loading && portfolioItems.length === 0 ? (
            <LoadingState message="Loading portfolio items..." size="lg" />
          ) : (
            <>
              {isStale && (
                <div className="fixed bottom-4 right-4 bg-background/80 backdrop-blur-sm p-2 rounded-md shadow-lg border border-border flex items-center gap-2 text-sm text-muted-foreground">
                  <Loader2 className="h-4 w-4 animate-spin" />
                  <span>Updating content...</span>
                </div>
              )}
              {children}
            </>
          )}
        </Suspense>
      </SanityPortfolioContext.Provider>
    </ErrorBoundary>
  );
}
