import { useTranslation } from 'react-i18next';
import { useLanguage } from '@/i18n/LanguageProvider';
import { cn } from '@/lib/utils';

const technologies = [
  {
    id: 'react',
    name: 'React',
    logo: 'https://cdn.jsdelivr.net/gh/devicons/devicon/icons/react/react-original.svg',
    category: 'frontend'
  },
  {
    id: 'nextjs',
    name: 'Next.js',
    logo: 'https://cdn.jsdelivr.net/gh/devicons/devicon/icons/nextjs/nextjs-original-wordmark.svg',
    category: 'frontend'
  },
  {
    id: 'vue',
    name: 'Vue.js',
    logo: 'https://cdn.jsdelivr.net/gh/devicons/devicon/icons/vuejs/vuejs-original.svg',
    category: 'frontend'
  },
  {
    id: 'angular',
    name: 'Angular',
    logo: 'https://cdn.jsdelivr.net/gh/devicons/devicon/icons/angularjs/angularjs-original.svg',
    category: 'frontend'
  },
  {
    id: 'typescript',
    name: 'TypeScript',
    logo: 'https://cdn.jsdelivr.net/gh/devicons/devicon/icons/typescript/typescript-original.svg',
    category: 'frontend'
  },
  {
    id: 'tailwind',
    name: 'Tailwind CSS',
    logo: 'https://upload.wikimedia.org/wikipedia/commons/d/d5/Tailwind_CSS_Logo.svg',
    category: 'frontend'
  },
  {
    id: 'node',
    name: 'Node.js',
    logo: 'https://cdn.jsdelivr.net/gh/devicons/devicon/icons/nodejs/nodejs-original.svg',
    category: 'backend'
  },
  {
    id: 'python',
    name: 'Python',
    logo: 'https://cdn.jsdelivr.net/gh/devicons/devicon/icons/python/python-original.svg',
    category: 'backend'
  },
  {
    id: 'express',
    name: 'Express',
    logo: 'https://cdn.jsdelivr.net/gh/devicons/devicon/icons/express/express-original-wordmark.svg',
    category: 'backend'
  },
  {
    id: 'mongodb',
    name: 'MongoDB',
    logo: 'https://cdn.jsdelivr.net/gh/devicons/devicon/icons/mongodb/mongodb-original.svg',
    category: 'backend'
  },
  {
    id: 'mysql',
    name: 'MySQL',
    logo: 'https://cdn.jsdelivr.net/gh/devicons/devicon/icons/mysql/mysql-original.svg',
    category: 'backend'
  },
  {
    id: 'postgresql',
    name: 'PostgreSQL',
    logo: 'https://cdn.jsdelivr.net/gh/devicons/devicon/icons/postgresql/postgresql-original.svg',
    category: 'backend'
  },
  {
    id: 'wordpress',
    name: 'WordPress',
    logo: 'https://cdn.jsdelivr.net/gh/devicons/devicon/icons/wordpress/wordpress-plain.svg',
    category: 'cms'
  },
  {
    id: 'shopify',
    name: 'Shopify',
    logo: 'https://cdn.shopify.com/shopifycloud/brochure/assets/brand-assets/shopify-logo-primary-logo-456baa801ee66a0a435671082365958316831c9960c480451dd0330bcdae304f.svg',
    category: 'cms'
  }
];

const Technologies = () => {
  const { t } = useTranslation();
  const { direction } = useLanguage();

  // Group technologies by category
  const frontendTech = technologies.filter(tech => tech.category === 'frontend');
  const backendTech = technologies.filter(tech => tech.category === 'backend');
  const cmsTech = technologies.filter(tech => tech.category === 'cms');

  return (
    <section className="py-16 md:py-24 bg-muted/30 dark:bg-muted/10 relative">
      {/* Background decorations */}
      <div className="absolute top-1/4 left-0 w-72 h-72 bg-primary/5 rounded-full blur-3xl -z-10"></div>
      <div className="absolute bottom-1/4 right-0 w-80 h-80 bg-secondary/5 rounded-full blur-3xl -z-10"></div>

      <div className="container">
        <div className="text-center max-w-3xl mx-auto mb-16">
          <span className="inline-block text-sm font-medium text-secondary bg-secondary/10 dark:bg-secondary/20 rounded-full px-3 py-1.5 mb-4">
            {t('home.technologies.subtitle', 'Our Tech Stack')}
          </span>

          <h2 className="text-3xl md:text-4xl font-bold mb-6 text-foreground">
            {t('home.technologies.title', 'Technologies We Work With')}
          </h2>

          <p className="text-lg text-muted-foreground">
            {t('home.technologies.description', 'We use modern, cutting-edge technologies to build robust, scalable, and high-performance web applications.')}
          </p>
        </div>

        <div className="space-y-12">
          {/* Frontend Technologies */}
          <div>
            <h3 className="text-xl font-semibold mb-6 text-foreground border-b border-border pb-2">
              {t('home.technologies.frontend', 'Frontend Technologies')}
            </h3>
            <div className="grid grid-cols-3 sm:grid-cols-4 md:grid-cols-6 gap-8 items-center justify-center">
              {frontendTech.map((tech, index) => (
                <div
                  key={tech.id}
                  className="flex flex-col items-center justify-center p-4 hover:scale-110 transition-transform duration-300 animate-fade-in"
                  style={{ animationDelay: `${index * 100}ms` }}
                >
                  <div className="w-16 h-16 mb-3 flex items-center justify-center bg-card rounded-lg p-2 shadow-sm">
                    <img
                      src={tech.logo}
                      alt={tech.name}
                      className="max-w-full max-h-full object-contain"
                    />
                  </div>
                  <span className="text-sm font-medium text-foreground">{tech.name}</span>
                </div>
              ))}
            </div>
          </div>

          {/* Backend Technologies */}
          <div>
            <h3 className="text-xl font-semibold mb-6 text-foreground border-b border-border pb-2">
              {t('home.technologies.backend', 'Backend Technologies')}
            </h3>
            <div className="grid grid-cols-3 sm:grid-cols-4 md:grid-cols-6 gap-8 items-center justify-center">
              {backendTech.map((tech, index) => (
                <div
                  key={tech.id}
                  className="flex flex-col items-center justify-center p-4 hover:scale-110 transition-transform duration-300 animate-fade-in"
                  style={{ animationDelay: `${index * 100}ms` }}
                >
                  <div className="w-16 h-16 mb-3 flex items-center justify-center bg-card rounded-lg p-2 shadow-sm">
                    <img
                      src={tech.logo}
                      alt={tech.name}
                      className="max-w-full max-h-full object-contain"
                    />
                  </div>
                  <span className="text-sm font-medium text-foreground">{tech.name}</span>
                </div>
              ))}
            </div>
          </div>

          {/* CMS Technologies */}
          <div>
            <h3 className="text-xl font-semibold mb-6 text-foreground border-b border-border pb-2">
              {t('home.technologies.cms', 'CMS & E-commerce')}
            </h3>
            <div className="grid grid-cols-3 sm:grid-cols-4 md:grid-cols-6 gap-8 items-center justify-center">
              {cmsTech.map((tech, index) => (
                <div
                  key={tech.id}
                  className="flex flex-col items-center justify-center p-4 hover:scale-110 transition-transform duration-300 animate-fade-in"
                  style={{ animationDelay: `${index * 100}ms` }}
                >
                  <div className="w-16 h-16 mb-3 flex items-center justify-center bg-card rounded-lg p-2 shadow-sm">
                    <img
                      src={tech.logo}
                      alt={tech.name}
                      className="max-w-full max-h-full object-contain"
                    />
                  </div>
                  <span className="text-sm font-medium text-foreground">{tech.name}</span>
                </div>
              ))}
            </div>
          </div>
        </div>
      </div>
    </section>
  );
};

export default Technologies;
