import { createClient } from '@sanity/client';
import imageUrlBuilder from '@sanity/image-url';
import { SanityImageSource } from '@sanity/image-url/lib/types/types';

// Custom error types for better error handling
class SanityAuthenticationError extends Error {
  constructor(message: string) {
    super(message);
    this.name = 'SanityAuthenticationError';
  }
}

class SanityCORSError extends Error {
  constructor(message: string) {
    super(message);
    this.name = 'SanityCORSError';
  }
}

// Define enhanced Sanity error types
interface SanityErrorRetry {
  attempt: number;
  delay: number;
}

interface SanityError extends Error {
  statusCode?: number;
  status?: number;
  details?: unknown;
  request?: {
    url?: string;
    method?: string;
    headers?: Record<string, string>;
  };
  _retry?: SanityErrorRetry;
}

// Validate environment variables
const requiredEnvVars = {
  projectId: import.meta.env.VITE_SANITY_PROJECT_ID,
  dataset: import.meta.env.VITE_SANITY_DATASET,
  token: import.meta.env.VITE_SANITY_TOKEN,
};

Object.entries(requiredEnvVars).forEach(([key, value]) => {
  if (!value) {
    throw new Error(`Missing required environment variable: VITE_SANITY_${key.toUpperCase()}`);
  }
});

// CDN configuration
const cdnConfig = {
  useCdn: true,
  // Configure cache durations for different content types
  cacheMaxAge: {
    default: 60 * 60, // 1 hour for most content
    static: 24 * 60 * 60, // 24 hours for static content
    drafts: 0 // No caching for drafts
  }
};

// Initialize the Sanity client with enhanced configuration
export const client = createClient({
  projectId: requiredEnvVars.projectId,
  dataset: requiredEnvVars.dataset,
  useCdn: cdnConfig.useCdn,
  apiVersion: '2023-05-03',
  token: requiredEnvVars.token,
  ignoreBrowserTokenWarning: true,
  perspective: 'published',
  stega: false
});

// Configure request monitoring and retry logic
const requestMonitor = {
  lastRequestTime: Date.now(),
  MAX_RETRIES: 3,
  RETRY_CODES: [429, 503], // Too many requests, Service unavailable
  RETRY_MESSAGES: ['ETIMEDOUT', 'ECONNRESET'],

  updateLastRequest() {
    this.lastRequestTime = Date.now();
  },

  checkStatus() {
    const now = Date.now();
    const timeSinceLastRequest = now - this.lastRequestTime;

    if (timeSinceLastRequest > 10000) {
      // Silent monitoring, no console output needed
    }
  },

  shouldRetry(error: Error | SanityError, attempt: number): boolean {
    if (attempt >= this.MAX_RETRIES) return false;

    const sanitizedError = error as SanityError;

    // Enhanced retry conditions
    const retryableStatusCodes = [
      429, // Too many requests
      500, // Internal server error
      502, // Bad gateway
      503, // Service unavailable
      504  // Gateway timeout
    ];

    const retryableErrorMessages = [
      'ETIMEDOUT',
      'ECONNRESET',
      'ECONNREFUSED',
      'socket hang up',
      'network timeout',
      'Failed to fetch'
    ];

    return (
      retryableStatusCodes.includes(sanitizedError.statusCode || 0) ||
      retryableErrorMessages.some(msg => error.message.toLowerCase().includes(msg.toLowerCase())) ||
      // Retry on network errors
      error instanceof TypeError ||
      // Retry on timeout errors
      error.name === 'TimeoutError'
    );
  }
};

// Enhance client.fetch with monitoring and retry logic
const originalFetch = client.fetch.bind(client);
client.fetch = async (...args) => {
  requestMonitor.updateLastRequest();

  let attempt = 0;
  while (true) {
    try {
      const result = await originalFetch(...args);
      return result;
    } catch (error) {
      if (requestMonitor.shouldRetry(error, attempt)) {
        const delay = Math.min(1000 * Math.pow(2, attempt), 5000);
        // Retry logic - keeping this log for debugging network issues
        console.log(`[Sanity] Retrying in ${delay}ms (attempt ${attempt + 1}/${requestMonitor.MAX_RETRIES})`);
        await new Promise(resolve => setTimeout(resolve, delay));
        attempt++;
      } else {
        throw error;
      }
    }
  }
};

// Start connection monitoring
setInterval(() => requestMonitor.checkStatus(), 10000);


// Initialize the image URL builder
const builder = imageUrlBuilder(client);

// Helper function to generate image URLs
export function urlFor(source: SanityImageSource) {
  return builder.image(source);
}

// Enhanced error handler
// Enhanced error handler with detailed error types
function handleSanityError(error: Error | SanityError, context?: string): never {
  const sanitizedError = error as SanityError;

  // Log detailed error information
  console.error(`[Sanity] API Error${context ? ` (${context})` : ''}:`, {
    name: error.name,
    message: error.message,
    status: sanitizedError.status,
    statusCode: sanitizedError.statusCode,
    timestamp: new Date().toISOString(),
    details: sanitizedError.details,
    request: sanitizedError.request
  });

  // Handle authentication errors
  if (sanitizedError.statusCode === 401 || sanitizedError.statusCode === 403) {
    throw new SanityAuthenticationError('Authentication failed. Please check your Sanity token.');
  }

  // Handle CORS errors
  if (error.name === 'TypeError' && error.message.includes('CORS')) {
    throw new SanityCORSError('CORS error occurred. Please check your CORS configuration in Sanity dashboard.');
  }

  // Network errors
  if (error instanceof TypeError && error.message.includes('network')) {
    throw new Error('Network error occurred. Please check your internet connection.');
  }

  // Timeout errors
  if (error.name === 'TimeoutError' || error.message.includes('timeout')) {
    throw new Error('Request timed out. Please try again.');
  }

  // Rate limiting
  if (sanitizedError.statusCode === 429) {
    throw new Error('Too many requests. Please try again later.');
  }

  // Generic server error
  if (sanitizedError.statusCode && sanitizedError.statusCode >= 500) {
    throw new Error('Server error occurred. Please try again later.');
  }

  throw error;
}

// Fetch all portfolio items with error handling

// Fetch all portfolio items
export async function getPortfolioItems() {
  try {
    const result = await client.fetch(`
      *[_type == "portfolio"] | order(publishedAt desc) {
        _id,
        title,
        slug,
        client,
        website,
        description,
        technologies,
        publishedAt,
        fullPageScreenshot,
        mainImage,
        additionalImages,
        detailedDescription,
        "year": dateTime(publishedAt).year
      }
    `);

    return result || [];
  } catch (error) {
    // Return empty array instead of throwing to prevent app crashes
    return [];
  }
}

// Fetch a single portfolio item by slug
export async function getPortfolioItemBySlug(slug: string) {
  try {
    const result = await client.fetch(`
      *[_type == "portfolio" && slug.current == $slug][0] {
        _id,
        title,
        slug,
        client,
        website,
        description,
        technologies,
        publishedAt,
        fullPageScreenshot,
        mainImage,
        additionalImages,
        detailedDescription
      }
    `, { slug });

    if (!result) {
      throw new Error(`Portfolio item with slug "${slug}" not found`);
    }

    return result;
  } catch (error) {
    handleSanityError(error as SanityError);
  }
}

// Fetch all blog posts
export async function getBlogPosts() {
  try {
    const result = await client.fetch(`
      *[_type == "blog"] | order(publishedAt desc) {
        _id,
        title,
        slug,
        excerpt,
        publishedAt,
        "author": author->name,
        "categories": categories[]->title,
        mainImage,
        body
      }
    `);

    return result;
  } catch (error) {
    handleSanityError(error as SanityError);
  }
}

// Fetch a single blog post by slug
export async function getBlogPostBySlug(slug: string) {
  try {
    const result = await client.fetch(`
      *[_type == "blog" && slug.current == $slug][0] {
        _id,
        title,
        slug,
        excerpt,
        publishedAt,
        "author": author->name,
        "categories": categories[]->title,
        mainImage,
        body
      }
    `, { slug });

    if (!result) {
      throw new Error(`Blog post with slug "${slug}" not found`);
    }

    return result;
  } catch (error) {
    handleSanityError(error as SanityError);
  }
}

// Fetch related blog posts by category
export async function getRelatedBlogPosts(categories: string[], limit = 2) {
  try {
    return await client.fetch(`
      *[_type == "blog" && count((categories[]->title)[@ in $categories]) > 0] | order(publishedAt desc)[0...$limit] {
        _id,
        title,
        slug,
        excerpt,
        publishedAt,
        "author": author->name,
        "categories": categories[]->title,
        mainImage
      }
    `, { categories, limit });
  } catch (error) {
    handleSanityError(error as SanityError);
  }
}

// Fetch related portfolio items
export async function getRelatedPortfolioItems(slug: string, limit = 3) {
  try {
    return await client.fetch(`
      *[_type == "portfolio" && slug.current != $slug] | order(publishedAt desc)[0...$limit] {
        _id,
        title,
        slug,
        client,
        description,
        technologies,
        fullPageScreenshot,
        mainImage
      }
    `, { slug, limit });
  } catch (error) {
    handleSanityError(error as SanityError);
  }
}
