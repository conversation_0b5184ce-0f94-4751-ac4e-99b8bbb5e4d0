import { useEffect, useState } from 'react';
import { useLocation } from 'react-router-dom';
import { cn } from '@/lib/utils';
import { Spinner } from '@/components/ui/spinner';

interface PageTransitionProps {
  children: React.ReactNode;
}

export function PageTransition({ children }: PageTransitionProps) {
  const location = useLocation();
  const [displayLocation, setDisplayLocation] = useState(location);
  const [transitionStage, setTransitionStage] = useState('fadeIn');

  useEffect(() => {
    if (location.pathname !== displayLocation.pathname) {
      setTransitionStage('fadeOut');
      // Use requestAnimationFrame to ensure smooth transition
      requestAnimationFrame(() => {
        setDisplayLocation(location);
        setTransitionStage('fadeIn');
      });
    }
  }, [location, displayLocation]);

  return (
    <div
      className={cn(
        'transition-all duration-200 ease-in-out',
        transitionStage === 'fadeIn'
          ? 'opacity-100 translate-y-0'
          : 'opacity-90 translate-y-1'
      )}
    >
      {children}
    </div>
  );
}
