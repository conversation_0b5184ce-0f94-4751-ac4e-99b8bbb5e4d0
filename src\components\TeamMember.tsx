import { Gith<PERSON>, <PERSON>ed<PERSON>, Twitter } from "lucide-react";
import { Card, CardContent } from "@/components/ui/card";
import { cn } from "@/lib/utils";

interface TeamMemberProps {
  name: string;
  role: string;
  bio: string;
  imageSrc: string;
  socialLinks?: {
    twitter?: string;
    linkedin?: string;
    github?: string;
  };
  className?: string;
}

export function TeamMember({ 
  name, 
  role, 
  bio, 
  imageSrc, 
  socialLinks = {}, 
  className 
}: TeamMemberProps) {
  return (
    <Card className={cn("overflow-hidden", className)}>
      <div className="aspect-[4/3] relative">
        <img 
          src={imageSrc} 
          alt={name} 
          className="object-cover w-full h-full"
          loading="lazy"
        />
      </div>
      <CardContent className="p-6">
        <h3 className="text-xl font-bold mb-1">{name}</h3>
        <p className="text-secondary font-medium mb-3">{role}</p>
        <p className="text-muted-foreground mb-4">{bio}</p>
        
        <div className="flex space-x-3">
          {socialLinks.twitter && (
            <a 
              href={socialLinks.twitter} 
              target="_blank" 
              rel="noopener noreferrer"
              className="text-muted-foreground hover:text-primary transition-colors"
              aria-label={`${name}'s Twitter`}
            >
              <Twitter size={18} />
            </a>
          )}
          {socialLinks.linkedin && (
            <a 
              href={socialLinks.linkedin} 
              target="_blank" 
              rel="noopener noreferrer"
              className="text-muted-foreground hover:text-primary transition-colors"
              aria-label={`${name}'s LinkedIn`}
            >
              <Linkedin size={18} />
            </a>
          )}
          {socialLinks.github && (
            <a 
              href={socialLinks.github} 
              target="_blank" 
              rel="noopener noreferrer"
              className="text-muted-foreground hover:text-primary transition-colors"
              aria-label={`${name}'s GitHub`}
            >
              <Github size={18} />
            </a>
          )}
        </div>
      </CardContent>
    </Card>
  );
}
