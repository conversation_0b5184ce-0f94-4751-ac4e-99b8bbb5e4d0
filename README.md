# CodeSafir - Web Deployment Agency

CodeSafir is a cutting-edge web development company ith modern technologies. This project showcases a professional, responsive website for a fictional web deployment agency specializing in infrastructure as code, CI/CD pipelines, container orchestration, and cloud migrations.

![CodeSafir Screenshot](https://images.unsplash.com/photo-1551434678-e076c223a692?ixlib=rb-4.0.3&auto=format&fit=crop&w=1200&q=80)

## Features

- **Bilingual Support**: Full English and Arabic language support
- **RTL Support**: Right-to-left layout for Arabic language
- **Responsive Design**: Fully responsive layout that works on all devices
- **Dark Mode**: Toggle between light and dark themes with localStorage persistence
- **Modern UI Components**: Built with shadcn/ui for a sleek, professional look
- **Type Safety**: Written in TypeScript for better developer experience
- **Performance Optimized**: Fast loading times and smooth animations
- **SEO Friendly**: Proper meta tags and semantic HTML

## Tech Stack

- **Vite**: For fast development and optimized builds
- **React**: UI library for building component-based interfaces
- **TypeScript**: For type safety and better developer experience
- **Tailwind CSS**: Utility-first CSS framework with JIT mode
- **shadcn/ui**: High-quality UI components built with Radix UI and Tailwind
- **React Router**: For client-side routing
- **i18next**: Internationalization framework for React
- **React-i18next**: React bindings for i18next

## Getting Started

### Prerequisites

- Node.js (v16 or later)
- npm or yarn

### Installation

1. Clone the repository:

```sh
git clone https://github.com/yourusername/codesafir.git
cd codesafir
```

2. Install dependencies:

```sh
npm install
# or
yarn
```

3. Start the development server:

```sh
npm run dev
# or
yarn dev
```

4. Open your browser and navigate to `http://localhost:8080`

## Building for Production

To create a production build:

```sh
npm run build
# or
yarn build
```

The build artifacts will be stored in the `dist/` directory.

## Docker Deployment

This project includes a Dockerfile for easy deployment:

1. Build the Docker image:

```sh
docker build -t codesafir .
```

2. Run the container:

```sh
docker run -p 80:80 codesafir
```

3. Access the application at `http://localhost`

## Project Structure

```
codesafir/
├── public/             # Static assets
├── src/
│   ├── components/     # Reusable UI components
│   │   ├── layout/     # Layout components (Navbar, Footer)
│   │   ├── sections/   # Page sections
│   │   └── ui/         # shadcn/ui components
│   ├── data/           # Data files (services, etc.)
│   ├── hooks/          # Custom React hooks
│   ├── i18n/           # Internationalization
│   │   ├── locales/    # Translation files
│   │   │   ├── en/     # English translations
│   │   │   └── ar/     # Arabic translations
│   │   └── i18n.ts     # i18n configuration
│   ├── lib/            # Utility functions
│   ├── pages/          # Page components
│   ├── App.tsx         # Main application component
│   ├── index.css       # Global styles
│   └── main.tsx        # Application entry point
├── .gitignore
├── components.json     # shadcn/ui configuration
├── Dockerfile          # Docker configuration
├── index.html          # HTML template
├── nginx.conf          # Nginx configuration for Docker
├── package.json
├── README.md
├── tailwind.config.ts  # Tailwind CSS configuration
├── tsconfig.json       # TypeScript configuration
└── vite.config.ts      # Vite configuration
```

## Customization

### Changing Colors

The color palette can be modified in `src/index.css` and `tailwind.config.ts`.

### Adding New Pages

1. Create a new page component in `src/pages/`
2. Add a new route in `src/App.tsx`

### Adding New Components

1. Create a new component in `src/components/`
2. Import and use it in your pages or other components

### Adding New Translations

1. Add new translation keys to `src/i18n/locales/en/translation.json` and `src/i18n/locales/ar/translation.json`
2. Use the translation keys in your components with the `t` function from `useTranslation()`

### Language Switching

The language switcher component is available in the navbar. It toggles between English and Arabic languages. The language preference is stored in localStorage.

## License

This project is licensed under the MIT License - see the LICENSE file for details.

## Acknowledgments

- [shadcn/ui](https://ui.shadcn.com/) for the beautiful UI components
- [Tailwind CSS](https://tailwindcss.com/) for the utility-first CSS framework
- [Vite](https://vitejs.dev/) for the fast development experience
- [React](https://reactjs.org/) for the UI library
- [TypeScript](https://www.typescriptlang.org/) for the type safety
- [i18next](https://www.i18next.com/) for the internationalization
- [React-i18next](https://react.i18next.com/) for the React bindings for i18next
