import { useRef } from "react";
import { Chevron<PERSON>eft, ChevronRight } from "lucide-react";
import { Button } from "@/components/ui/button";
import { TeamMember } from "@/components/TeamMember";
import { useTranslation } from "react-i18next";
import { useLanguage } from "@/i18n/LanguageProvider";
import { cn } from "@/lib/utils";
import {
  Carousel,
  CarouselContent,
  CarouselItem,
  CarouselNext,
  CarouselPrevious,
} from "@/components/ui/carousel";

const teamMembers = [
  {
    id: "sarah-johnson",
    name: "<PERSON>",
    role: "CEO & Founder",
    bio: "With over 15 years in cloud infrastructure, <PERSON> leads our vision to revolutionize web deployment.",
    imageSrc: "https://images.unsplash.com/photo-1573496359142-b8d87734a5a2?ixlib=rb-4.0.3&auto=format&fit=crop&w=600&q=80",
    socialLinks: {
      twitter: "#",
      linkedin: "#",
      github: "#"
    }
  },
  {
    id: "micha<PERSON>-chen",
    name: "<PERSON>",
    role: "CTO",
    bio: "<PERSON> brings expertise in containerization and Kubernetes to architect scalable deployment solutions.",
    imageSrc: "https://images.unsplash.com/photo-**********-0b93528c311a?ixlib=rb-4.0.3&auto=format&fit=crop&w=600&q=80",
    socialLinks: {
      twitter: "#",
      linkedin: "#",
      github: "#"
    }
  },
  {
    id: "aisha-patel",
    name: "Aisha Patel",
    role: "DevOps Lead",
    bio: "Aisha specializes in CI/CD pipelines and has implemented automated workflows for Fortune 500 companies.",
    imageSrc: "https://images.unsplash.com/photo-1580489944761-15a19d654956?ixlib=rb-4.0.3&auto=format&fit=crop&w=600&q=80",
    socialLinks: {
      linkedin: "#",
      github: "#"
    }
  },
  {
    id: "james-wilson",
    name: "James Wilson",
    role: "Cloud Architect",
    bio: "James is an AWS certified professional with deep expertise in infrastructure as code and serverless architectures.",
    imageSrc: "https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?ixlib=rb-4.0.3&auto=format&fit=crop&w=600&q=80",
    socialLinks: {
      twitter: "#",
      linkedin: "#",
      github: "#"
    }
  },
  {
    id: "elena-rodriguez",
    name: "Elena Rodriguez",
    role: "Security Specialist",
    bio: "Elena ensures all our deployment solutions meet the highest security standards and compliance requirements.",
    imageSrc: "https://images.unsplash.com/photo-1598550874175-4d0ef436c909?ixlib=rb-4.0.3&auto=format&fit=crop&w=600&q=80",
    socialLinks: {
      linkedin: "#",
      github: "#"
    }
  },
  {
    id: "david-kim",
    name: "David Kim",
    role: "Frontend Engineer",
    bio: "David creates beautiful and functional user interfaces that make complex deployment tasks intuitive.",
    imageSrc: "https://images.unsplash.com/photo-1539571696357-5a69c17a67c6?ixlib=rb-4.0.3&auto=format&fit=crop&w=600&q=80",
    socialLinks: {
      twitter: "#",
      github: "#"
    }
  }
];

export function TeamCarousel() {
  const carouselRef = useRef(null);
  const { t } = useTranslation();
  const { direction } = useLanguage();

  return (
    <div className="relative">
      <Carousel
        ref={carouselRef}
        opts={{
          align: "start",
          loop: true,
          direction: direction === 'rtl' ? 'rtl' : 'ltr',
        }}
        className="w-full"
        dir={direction}
      >
        <CarouselContent className={direction === 'rtl' ? '-mr-4' : '-ml-4'}>
          {teamMembers.map((member) => (
            <CarouselItem
              key={member.id}
              className={cn(
                "md:basis-1/2 lg:basis-1/3",
                direction === 'rtl' ? 'pr-4' : 'pl-4'
              )}
            >
              <TeamMember
                name={t(`team.${member.id}.name`, member.name)}
                role={t(`team.${member.id}.role`, member.role)}
                bio={t(`team.${member.id}.bio`, member.bio)}
                imageSrc={member.imageSrc}
                socialLinks={member.socialLinks}
              />
            </CarouselItem>
          ))}
        </CarouselContent>
        <div className={cn(
          "hidden md:flex gap-2 mt-6",
          direction === 'rtl' ? 'justify-start' : 'justify-end'
        )}>
          <Button
            variant="outline"
            size="icon"
            className="h-8 w-8 rounded-full"
            onClick={() => {
              if (carouselRef.current) {
                (carouselRef.current as any).scrollPrev();
              }
            }}
            aria-label={t('common.previous', 'Previous slide')}
          >
            {direction === 'rtl' ? <ChevronRight className="h-4 w-4" /> : <ChevronLeft className="h-4 w-4" />}
            <span className="sr-only">{t('common.previous', 'Previous slide')}</span>
          </Button>
          <Button
            variant="outline"
            size="icon"
            className="h-8 w-8 rounded-full"
            onClick={() => {
              if (carouselRef.current) {
                (carouselRef.current as any).scrollNext();
              }
            }}
            aria-label={t('common.next', 'Next slide')}
          >
            {direction === 'rtl' ? <ChevronLeft className="h-4 w-4" /> : <ChevronRight className="h-4 w-4" />}
            <span className="sr-only">{t('common.next', 'Next slide')}</span>
          </Button>
        </div>
        <div className="md:hidden">
          <CarouselPrevious />
          <CarouselNext />
        </div>
      </Carousel>
    </div>
  );
}
