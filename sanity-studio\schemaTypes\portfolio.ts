export default {
  name: 'portfolio',
  type: 'document',
  title: 'Portfolio Projects',
  fields: [
    {
      name: 'title',
      type: 'string',
      title: 'Project Title',
      validation: (Rule: any) => Rule.required(),
    },
    {
      name: 'slug',
      type: 'slug',
      title: 'Slug',
      options: {
        source: 'title',
        maxLength: 96,
      },
      validation: (Rule: any) => Rule.required(),
    },
    {
      name: 'client',
      type: 'string',
      title: 'Client Name',
    },
    {
      name: 'website',
      type: 'url',
      title: 'Website URL',
      description: 'Leave empty for internal projects or projects without public websites. The UI will show "Internal Project" instead.',
    },
    {
      name: 'fullPageScreenshot',
      type: 'image',
      title: 'Full Page Screenshot',
      description: 'Complete website screenshot showing the full page layout. This will be the primary image displayed in portfolio cards.',
      options: {
        hotspot: true,
      },
    },
    {
      name: 'mainImage',
      type: 'image',
      title: 'Main Image (Legacy)',
      description: 'Legacy main image field. Use Full Page Screenshot instead for new projects.',
      options: {
        hotspot: true,
      },
    },
    {
      name: 'additionalImages',
      type: 'array',
      title: 'Additional Images',
      of: [
        {
          type: 'image',
          options: {
            hotspot: true,
          },
        },
      ],
    },
    {
      name: 'technologies',
      type: 'array',
      title: 'Technologies Used',
      of: [{ type: 'string' }],
    },
    {
      name: 'description',
      type: 'text',
      title: 'Description',
      validation: (Rule: any) => Rule.required(),
    },
    {
      name: 'detailedDescription',
      type: 'array',
      title: 'Detailed Description',
      of: [
        {
          type: 'block',
        },
      ],
    },
    {
      name: 'publishedAt',
      type: 'datetime',
      title: 'Published At',
    },
  ],
  preview: {
    select: {
      title: 'title',
      client: 'client',
      media: 'mainImage',
    },
    prepare({ title, client, media }: { title: string; client?: string; media: any }) {
      return {
        title,
        subtitle: client ? `Client: ${client}` : '',
        media,
      }
    },
  },
} 