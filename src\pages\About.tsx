import { useTranslation } from "react-i18next";
import { <PERSON> } from "react-router-dom";
import { ArrowRight, Code, Layers, Palette, Globe, Smartphone, CheckCircle2, Sparkles, Users } from "lucide-react";
import BasicHeader from "@/components/layout/BasicHeader";
import Footer from "@/components/layout/Footer";
import { useLanguage } from "@/i18n/LanguageProvider";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Card, CardContent } from "@/components/ui/card";
import { cn } from "@/lib/utils";

const About = () => {
  const { t } = useTranslation();
  const { direction } = useLanguage();
  const isRTL = direction === 'rtl';

  // Tech badges for the hero section
  const techBadges = [
    { icon: Code, label: "React" },
    { icon: Layers, label: "Next.js" },
    { icon: Palette, label: "UI/UX" },
    { icon: Globe, label: "Web Apps" },
    { icon: Smartphone, label: "Responsive" }
  ];

  // Our approach steps
  const approachSteps = [
    {
      id: "discovery",
      title: t('services.process.discovery.title'),
      description: t('services.process.discovery.description'),
      color: "bg-blue-500/10 text-blue-500"
    },
    {
      id: "planning",
      title: t('services.process.planning.title'),
      description: t('services.process.planning.description'),
      color: "bg-purple-500/10 text-purple-500"
    },
    {
      id: "design",
      title: t('services.process.design.title'),
      description: t('services.process.design.description'),
      color: "bg-pink-500/10 text-pink-500"
    },
    {
      id: "development",
      title: t('services.process.development.title'),
      description: t('services.process.development.description'),
      color: "bg-secondary/10 text-secondary"
    }
  ];

  // Company values with icons
  const values = [
    {
      key: "expertise",
      icon: CheckCircle2,
      title: t('about.values.expertise.title'),
      description: t('about.values.expertise.description')
    },
    {
      key: "innovation",
      icon: Sparkles,
      title: t('about.values.innovation.title'),
      description: t('about.values.innovation.description')
    },
    {
      key: "partnership",
      icon: Users,
      title: t('about.values.partnership.title'),
      description: t('about.values.partnership.description')
    }
  ];

  return (
    <div className="min-h-screen flex flex-col">
      <BasicHeader />
      <main className="flex-grow pt-20">
        {/* Hero Section - Enhanced with animations and better visual hierarchy */}
        <section className="py-20 md:py-28 bg-muted/30 dark:bg-muted/10 relative overflow-hidden">
          {/* Decorative elements */}
          <div className="absolute top-0 left-0 w-72 h-72 bg-primary/5 rounded-full blur-3xl -z-10 opacity-70"></div>
          <div className="absolute bottom-0 right-0 w-96 h-96 bg-secondary/5 rounded-full blur-3xl -z-10 opacity-70"></div>

          <div className="container">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-12 items-center">
              <div className="animate-fade-in" style={{ animationDelay: '100ms' }}>
                <Badge variant="secondary" className="mb-6 px-4 py-1.5 text-sm font-medium">
                  {t('about.hero.title')}
                </Badge>
                <h1 className="text-4xl md:text-5xl lg:text-6xl font-bold mb-6 text-foreground leading-tight">
                  {t('about.hero.subtitle')}
                </h1>
                <p className="text-lg md:text-xl text-muted-foreground mb-8 max-w-lg">
                  {t('about.mission.description')}
                </p>

                {/* Tech badges */}
                <div className={cn(
                  "flex flex-wrap gap-2 mb-8",
                  isRTL ? "justify-end" : "justify-start"
                )}>
                  {techBadges.map((badge) => {
                    const Icon = badge.icon;
                    return (
                      <Badge
                        key={badge.label}
                        variant="outline"
                        className="bg-background/80 backdrop-blur-sm border-border/50 py-1.5 px-3 rounded-full"
                      >
                        <Icon className="h-3.5 w-3.5 mr-1.5 text-primary" />
                        <span>{badge.label}</span>
                      </Badge>
                    );
                  })}
                </div>

                {/* CTA Button */}
                <Button asChild size="lg" className="rounded-full">
                  <Link to="/contact" className={cn(
                    "group",
                    isRTL ? "flex flex-row-reverse" : "flex"
                  )}>
                    {t('common.contactUs')}
                    <ArrowRight className={cn(
                      "h-4 w-4 transition-transform",
                      isRTL
                        ? "mr-2 group-hover:-translate-x-1 rotate-180"
                        : "ml-2 group-hover:translate-x-1"
                    )} />
                  </Link>
                </Button>
              </div>

              <div className="relative animate-fade-in" style={{ animationDelay: '300ms' }}>
                <div className="aspect-square relative z-10 rounded-2xl overflow-hidden shadow-xl">
                  <img
                    src="https://images.unsplash.com/photo-1522071820081-009f0129c71c?ixlib=rb-4.0.3&auto=format&fit=crop&w=1200&q=80"
                    alt="CodeSafir Team"
                    className="object-cover w-full h-full hover:scale-105 transition-transform duration-700"
                  />
                </div>
                <div className="absolute -bottom-6 -right-6 w-32 h-32 bg-secondary/20 rounded-full z-0 animate-pulse"></div>
                <div className="absolute -top-6 -left-6 w-24 h-24 bg-primary/20 rounded-full z-0 animate-pulse" style={{ animationDelay: '1s' }}></div>
              </div>
            </div>
          </div>
        </section>

        {/* Mission Section - Redesigned with better visual appeal */}
        <section className="py-20 md:py-28 relative">
          {/* Decorative elements */}
          <div className="absolute top-1/3 left-1/4 w-64 h-64 bg-accent/5 rounded-full blur-3xl -z-10 opacity-50"></div>

          <div className="container">
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-16 items-center">
              <div className="order-2 lg:order-1">
                <div className="grid grid-cols-1 sm:grid-cols-2 gap-6 md:gap-8">
                  {values.map((value, index) => {
                    const Icon = value.icon;
                    return (
                      <Card
                        key={value.key}
                        className={cn(
                          "border border-border hover:shadow-lg transition-all duration-300 hover:-translate-y-1 group",
                          index === 0 && "sm:col-span-2"
                        )}
                      >
                        <CardContent className="p-6 md:p-8">
                          <div className="p-3 bg-primary/10 rounded-full w-12 h-12 flex items-center justify-center mb-6 group-hover:bg-primary/20 transition-colors">
                            <Icon className="h-6 w-6 text-primary" />
                          </div>
                          <h3 className="text-xl font-bold mb-4 text-foreground">{value.title}</h3>
                          <p className="text-muted-foreground">{value.description}</p>
                        </CardContent>
                      </Card>
                    );
                  })}
                </div>
              </div>

              <div className="order-1 lg:order-2">
                <Badge variant="outline" className="mb-4 px-3 py-1 text-sm font-medium bg-primary/10 text-primary">
                  {t('about.mission.title')}
                </Badge>
                <h2 className="text-3xl md:text-4xl font-bold mb-6 text-foreground">
                  {t('about.mission.title')}
                </h2>
                <p className="text-lg text-muted-foreground mb-8">
                  {t('about.mission.description')}
                </p>
                <p className="text-lg text-muted-foreground mb-8">
                  We're passionate about creating exceptional web experiences that help businesses thrive in the digital world. Our team combines technical expertise with creative design to deliver solutions that are not only visually stunning but also highly functional and user-friendly.
                </p>
                <Button variant="outline" asChild className={cn(
                  "group",
                  isRTL ? "flex flex-row-reverse" : "flex"
                )}>
                  <Link to="/services">
                    {t('common.learnMore')}
                    <ArrowRight className={cn(
                      "h-4 w-4 transition-transform",
                      isRTL
                        ? "mr-2 group-hover:-translate-x-1 rotate-180"
                        : "ml-2 group-hover:translate-x-1"
                    )} />
                  </Link>
                </Button>
              </div>
            </div>
          </div>
        </section>

        {/* Our Approach Section - New section with process flow */}
        <section className="py-20 md:py-28 bg-muted/30 dark:bg-muted/10 relative">
          {/* Decorative elements */}
          <div className="absolute bottom-0 right-1/4 w-80 h-80 bg-primary/5 rounded-full blur-3xl -z-10 opacity-60"></div>

          <div className="container">
            <div className="text-center max-w-3xl mx-auto mb-16">
              <Badge variant="secondary" className="mb-4 px-3 py-1 text-sm font-medium">
                {t('services.process.title')}
              </Badge>
              <h2 className="text-3xl md:text-4xl font-bold mb-6 text-foreground">
                {t('services.process.title')}
              </h2>
              <p className="text-lg text-muted-foreground">
                Our structured approach ensures we deliver exceptional web solutions that meet your business objectives and exceed user expectations.
              </p>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8">
              {approachSteps.map((step, index) => (
                <div
                  key={step.id}
                  className="relative group"
                >
                  <div className="bg-card border border-border rounded-xl p-6 md:p-8 h-full hover:shadow-lg transition-all duration-300 hover:-translate-y-1">
                    <div className={cn(
                      "w-12 h-12 rounded-full flex items-center justify-center mb-6",
                      step.color
                    )}>
                      <span className="text-lg font-bold">0{index + 1}</span>
                    </div>
                    <h3 className="text-xl font-bold mb-4 text-foreground">{step.title}</h3>
                    <p className="text-muted-foreground">{step.description}</p>
                  </div>

                  {/* Connector line between steps (hidden on mobile) */}
                  {index < approachSteps.length - 1 && (
                    <div className="hidden lg:block absolute top-1/2 -right-4 w-8 h-0.5 bg-border">
                      <div className="absolute -right-1 -top-1 w-2 h-2 bg-primary rounded-full"></div>
                    </div>
                  )}
                </div>
              ))}
            </div>

            <div className="text-center mt-16">
              <Button asChild>
                <Link to="/portfolio" className={cn(
                  "group",
                  isRTL ? "flex flex-row-reverse" : "flex"
                )}>
                  {t('portfolio.hero.title')}
                  <ArrowRight className={cn(
                    "h-4 w-4 transition-transform",
                    isRTL
                      ? "mr-2 group-hover:-translate-x-1 rotate-180"
                      : "ml-2 group-hover:translate-x-1"
                  )} />
                </Link>
              </Button>
            </div>
          </div>
        </section>
      </main>
      <Footer />
    </div>
  );
};

export default About;
