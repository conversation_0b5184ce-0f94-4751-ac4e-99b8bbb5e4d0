@import url('https://fonts.googleapis.com/css2?family=Inter:wght@400;500;600;700&family=Montserrat:wght@500;600;700;800&display=swap');

@tailwind base;
@tailwind components;
@tailwind utilities;

@layer base {
  :root {
    --background: 0 0% 96%;
    /* #F4F4F4 */
    --foreground: 0 0% 20%;
    /* #333333 */

    --card: 0 0% 100%;
    --card-foreground: 0 0% 20%;

    --popover: 0 0% 100%;
    --popover-foreground: 0 0% 20%;

    --primary: 234 62% 27%;
    /* #1A1F71 Midnight Blue */
    --primary-foreground: 0 0% 100%;

    --secondary: 169 100% 37%;
    /* #00BFA6 Crescent Teal */
    --secondary-foreground: 0 0% 100%;

    --accent: 49 100% 50%;
    /* #FFD600 Solar Yellow */
    --accent-foreground: 0 0% 20%;

    --muted: 0 0% 96%;
    --muted-foreground: 0 0% 45%;

    --destructive: 0 84.2% 60.2%;
    --destructive-foreground: 0 0% 98%;

    --border: 0 0% 90%;
    --input: 0 0% 90%;
    --ring: 234 62% 27%;

    --radius: 0.5rem;
  }

  .dark {
    --background: 0 0% 10%;
    --foreground: 0 0% 98%;

    --card: 0 0% 15%;
    --card-foreground: 0 0% 98%;

    --popover: 0 0% 15%;
    --popover-foreground: 0 0% 98%;

    --primary: 234 62% 27%;
    /* #1A1F71 Midnight Blue */
    --primary-foreground: 0 0% 100%;

    --secondary: 169 100% 37%;
    /* #00BFA6 Crescent Teal */
    --secondary-foreground: 0 0% 100%;

    --accent: 49 100% 50%;
    /* #FFD600 Solar Yellow */
    --accent-foreground: 0 0% 20%;

    --muted: 0 0% 15%;
    --muted-foreground: 0 0% 65%;

    --destructive: 0 62.8% 30.6%;
    --destructive-foreground: 0 0% 98%;

    --border: 0 0% 20%;
    --input: 0 0% 20%;
    --ring: 234 62% 27%;
  }
}

@layer base {
  * {
    @apply border-border;
  }

  body {
    @apply bg-background text-foreground font-sans;
  }

  h1,
  h2,
  h3,
  h4,
  h5,
  h6 {
    @apply font-display;
  }

  /* RTL Support */
  [dir="rtl"] .space-x-2> :not([hidden])~ :not([hidden]) {
    --tw-space-x-reverse: 1;
  }

  [dir="rtl"] .space-x-3> :not([hidden])~ :not([hidden]) {
    --tw-space-x-reverse: 1;
  }

  [dir="rtl"] .space-x-4> :not([hidden])~ :not([hidden]) {
    --tw-space-x-reverse: 1;
  }

  [dir="rtl"] .space-x-5> :not([hidden])~ :not([hidden]) {
    --tw-space-x-reverse: 1;
  }

  [dir="rtl"] .space-x-6> :not([hidden])~ :not([hidden]) {
    --tw-space-x-reverse: 1;
  }

  [dir="rtl"] .space-x-8> :not([hidden])~ :not([hidden]) {
    --tw-space-x-reverse: 1;
  }
}

.container {
  @apply px-4 md:px-6 max-w-7xl mx-auto;
}