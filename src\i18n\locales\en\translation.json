{"common": {"getStarted": "Get Started", "learnMore": "Learn More", "contactUs": "Contact Us", "readMore": "Read More", "viewAll": "View All", "submit": "Submit", "loading": "Loading...", "error": "Error", "success": "Success", "required": "Required", "invalidEmail": "Invalid email address", "features": "Features", "andMore": "And more...", "switchLanguage": "Switch language", "toggleTheme": "Toggle theme", "lightTheme": "Light", "darkTheme": "Dark", "systemTheme": "System", "viewProject": "View project", "previous": "Previous slide", "next": "Next slide", "whatsappChat": "Chat with us on WhatsApp", "whatsappMessage": "Hello, I would like to learn more about CodeSafir's services."}, "nav": {"home": "Home", "services": "Services", "about": "About", "portfolio": "Portfolio", "blog": "Blog", "contact": "Contact", "language": "العربية"}, "home": {"hero": {"title": "Your Ambassador to the Web", "subtitle": "We build cutting-edge web applications with modern technologies and best practices", "cta": "Get Started", "clients": "satisfied clients", "rating": "client satisfaction rating", "agency": "Premium Web Development Agency", "mainTitle": "We Build <em>Exceptional</em> Web Experiences", "description": "Transforming your ideas into stunning, high-performance websites and web applications. Custom solutions tailored to your business needs.", "techBadges": {"react": "React", "nodejs": "Node.js", "uiux": "UI/UX", "webapps": "Web Apps", "responsive": "Responsive"}}, "howItWorks": {"subtitle": "Our Process", "title": "Our Web Development Process", "description": "We follow a structured, collaborative approach to deliver exceptional websites that meet your business objectives and exceed user expectations.", "steps": {"discovery": {"title": "Discovery & Planning", "description": "We start by understanding your business goals, target audience, and requirements to create a detailed project roadmap."}, "design": {"title": "Design & Prototyping", "description": "Our designers create wireframes and interactive prototypes, focusing on user experience, brand consistency, and visual appeal."}, "development": {"title": "Development", "description": "Our developers bring the designs to life using modern technologies and frameworks, ensuring clean, efficient, and maintainable code."}, "testing": {"title": "Testing & Launch", "description": "We thoroughly test your website across devices and browsers before deploying it to production with minimal downtime."}, "support": {"title": "Maintenance & Support", "description": "We provide ongoing support, regular updates, and performance monitoring to ensure your website remains secure and effective."}}}, "services": {"title": "Our Services", "subtitle": "Expert web development solutions tailored to your business goals", "customApps": {"title": "Custom Web Applications", "description": "Bespoke web applications engineered with cutting-edge technologies to transform your business ideas into powerful digital solutions"}, "ecommerce": {"title": "E-commerce Development", "description": "High-performance online stores with intuitive user experiences, secure payment processing, and advanced inventory management systems"}, "headlessCms": {"title": "Headless CMS Solutions", "description": "Modern content management architectures that deliver exceptional performance across websites, mobile apps, and all digital touchpoints"}, "cloudMigration": {"title": "Cloud & DevOps Services", "description": "Expert migration and optimization of your applications in the cloud with CI/CD automation for improved scalability, security and reliability"}}, "portfolio": {"title": "Our Work", "subtitle": "Check out some of our recent projects", "viewProject": "View Project"}, "caseStudies": {"subtitle": "Our Portfolio", "title": "Recent Projects", "description": "Explore our latest web development projects and see how we've helped businesses achieve their digital goals.", "luxury-ecommerce": {"title": "Luxury Fashion E-commerce", "description": "Designed and developed a high-end e-commerce platform with custom product configurator, resulting in a 45% increase in average order value."}, "real-estate-portal": {"title": "Real Estate Portal", "description": "Built a responsive real estate platform with advanced search filters, interactive maps, and virtual tours, improving lead generation by 60%."}, "healthcare-app": {"title": "Healthcare Patient Portal", "description": "Developed a secure, HIPAA-compliant patient portal with appointment scheduling, telemedicine integration, and medical records access."}}, "technologies": {"subtitle": "Our Tech Stack", "title": "Technologies We Work With", "description": "We use modern, cutting-edge technologies to build robust, scalable, and high-performance web applications.", "frontend": "Frontend Technologies", "backend": "Backend Technologies", "cms": "CMS & E-commerce"}, "testimonials": {"title": "What Our Clients Say", "subtitle": "Hear from businesses we've helped succeed"}, "cta": {"title": "Ready to Transform Your Web Presence?", "subtitle": "Let's discuss how we can help you achieve your goals", "button": "Contact Us Today", "description": "Whether you need a brand new website, a redesign, or custom web application, our team is ready to bring your vision to life with cutting-edge technology and stunning design.", "primaryButton": "Start Your Project", "secondaryButton": "Schedule a Consultation"}}, "about": {"hero": {"title": "About CodeSafir", "subtitle": "Your ambassador to the web"}, "mission": {"title": "Our Mission", "description": "At CodeSafir, we believe that deploying web applications should be seamless, secure, and scalable. Our team of experts is dedicated to helping businesses of all sizes navigate the complexities of modern web deployment."}, "team": {"title": "Meet Our Team", "subtitle": "The talented people behind our success", "description": "Our diverse team brings together expertise from various backgrounds to deliver exceptional web development solutions."}, "timeline": {"title": "Our Journey", "subtitle": "Key milestones in our company's history", "description": "From our founding to today, we've grown and evolved to better serve our clients' web development needs."}, "careers": {"title": "Careers"}, "values": {"expertise": {"title": "Expertise", "description": "Our team brings decades of combined experience in web development, design, and deployment solutions."}, "innovation": {"title": "Innovation", "description": "We stay at the forefront of technology to provide cutting-edge solutions that drive business growth."}, "partnership": {"title": "Partnership", "description": "We work closely with our clients to understand their unique needs and deliver tailored solutions."}}, "milestones": {"0": {"title": "CodeSafir Founded", "description": "CodeSafir was founded with a mission to simplify web development for businesses of all sizes."}, "1": {"title": "First Enterprise Client", "description": "Secured our first enterprise client and successfully delivered a complex web application."}, "2": {"title": "Technology Expansion", "description": "Expanded our technology stack to include React, Next.js, and modern frontend frameworks."}, "3": {"title": "Strategic Partnerships", "description": "Formed strategic partnerships with leading technology providers to enhance our service offerings."}, "4": {"title": "International Expansion", "description": "Expanded our services to international clients, with a focus on multilingual web solutions."}, "5": {"title": "Award-Winning Projects", "description": "Recognized for excellence in web development with multiple industry awards."}}}, "team": {"sarah-johnson": {"name": "<PERSON>", "role": "CEO & Founder", "bio": "With over 15 years in web development, <PERSON> leads our vision to revolutionize web solutions."}, "michael-chen": {"name": "<PERSON>", "role": "CTO", "bio": "<PERSON> brings expertise in modern web technologies to architect scalable development solutions."}, "aisha-patel": {"name": "<PERSON><PERSON>", "role": "DevOps Lead", "bio": "Aisha specializes in CI/CD pipelines and has implemented automated workflows for Fortune 500 companies."}, "james-wilson": {"name": "<PERSON>", "role": "Cloud Architect", "bio": "<PERSON> is a certified professional with deep expertise in infrastructure as code and serverless architectures."}, "elena-rodriguez": {"name": "<PERSON>", "role": "Security Specialist", "bio": "<PERSON> ensures all our development solutions meet the highest security standards and compliance requirements."}, "david-kim": {"name": "<PERSON>", "role": "Frontend Engineer", "bio": "<PERSON> creates beautiful and functional user interfaces that make complex web applications intuitive."}}, "services": {"hero": {"title": "Our Services", "subtitle": "Comprehensive web development solutions for your business"}, "process": {"title": "Our Process", "discovery": {"title": "Discovery", "description": "We start by understanding your business goals and requirements"}, "planning": {"title": "Planning", "description": "We create a detailed plan and roadmap for your project"}, "design": {"title": "Design", "description": "We design beautiful and intuitive user interfaces"}, "development": {"title": "Development", "description": "We build your application using modern technologies"}, "testing": {"title": "Testing", "description": "We thoroughly test your application to ensure quality"}, "deployment": {"title": "Deployment", "description": "We deploy your application to production"}, "maintenance": {"title": "Maintenance", "description": "We provide ongoing support and maintenance"}}, "caseStudies": {"title": "Case Studies", "subtitle": "See how we've helped businesses transform their web presence", "viewAll": "View all case studies"}}, "features": {"subtitle": "Our Services", "title": "Comprehensive Web Development Services", "description": "We offer end-to-end web development solutions to help businesses establish a powerful online presence with beautiful, functional, and high-performing websites.", "custom-web-development": {"title": "Custom Web Development", "description": "Bespoke websites and web applications built with modern frameworks like React, Vue, and Angular to meet your specific business needs."}, "responsive-design": {"title": "Responsive Design", "description": "Mobile-first websites that look and function perfectly on all devices, from smartphones to desktops, ensuring a seamless user experience."}, "ecommerce-solutions": {"title": "E-commerce Solutions", "description": "Custom online stores with secure payment gateways, inventory management, and seamless checkout experiences to boost your sales."}, "ui-ux-design": {"title": "UI/UX Design", "description": "User-centered design that combines aesthetics with functionality, creating intuitive interfaces that delight your users."}, "web-app-development": {"title": "Web App Development", "description": "Progressive Web Apps (PWAs) and Single Page Applications (SPAs) that offer native-like experiences with offline capabilities."}, "performance-optimization": {"title": "Performance Optimization", "description": "Speed up your website with advanced optimization techniques, improving load times, user experience, and search engine rankings."}, "cms-development": {"title": "CMS Development", "description": "Custom content management systems or integration with platforms like WordPress, Strapi, or Contentful for easy content updates."}, "seo-services": {"title": "SEO Services", "description": "Search engine optimization to improve your website's visibility, drive organic traffic, and increase conversions."}, "viewAllServices": "View all our services"}, "stats": {"subtitle": "By the numbers", "title": "Proven Track Record of Success", "description": "Our web development expertise is reflected in our impressive metrics and the success of our clients across various industries.", "projects": {"label": "Projects Completed"}, "clients": {"label": "Happy Clients"}, "satisfaction": {"label": "Client Satisfaction"}, "support": {"label": "Support Available"}}, "testimonials": {"title": "Client Testimonials", "heading": "What Our Clients Say", "subtitle": "Don't just take our word for it. Here's what our clients have to say about our web development services."}, "footer": {"company": "Company", "services": "Services", "resources": "Resources", "contact": "Contact", "legal": {"title": "Legal", "privacy": "Privacy Policy", "terms": "Terms of Service"}, "copyright": "All rights reserved."}, "notFound": {"message": "Oops! We couldn't find the page you're looking for.", "returnHome": "Return to Home"}, "portfolio": {"title": "Our Portfolio", "description": "Explore our work and see how we've helped businesses achieve their digital goals", "noWebsite": "Internal Project", "hero": {"title": "Our Portfolio", "subtitle": "Explore our work and see how we've helped businesses achieve their digital goals"}, "filters": {"all": "All Projects", "web": "Web Development", "ecommerce": "E-commerce", "cms": "CMS Solutions", "mobile": "Mobile Apps"}, "projectDetails": {"client": "Client", "year": "Year", "technologies": "Technologies Used", "description": "Description", "challenge": "The Challenge", "solution": "Our Solution", "results": "The Results", "testimonial": "Client Testimonial", "viewLive": "View Live Site", "noWebsite": "This project is not publicly accessible or is an internal application.", "discussProject": "Discuss Similar Project", "relatedProjects": "Related Projects", "relatedArticles": "Related Articles", "viewAllArticles": "View All Articles", "backToPortfolio": "Back to Portfolio"}}, "contact": {"hero": {"title": "Contact Us", "subtitle": "Get in touch with our team"}, "form": {"name": "Name", "email": "Email", "phone": "Phone (optional)", "company": "Company (Optional)", "subject": "Subject", "message": "Message", "submit": "Send Message", "success": "Your message has been sent. We'll get back to you soon!", "error": "There was an error sending your message. Please try again."}, "info": {"title": "Contact Information", "address": "Address", "email": "Email", "whatsappMessage": "Chat with us on WhatsApp"}, "faq": {"title": "Frequently Asked Questions", "subtitle": "Find answers to common questions about our services and how we can help with your web deployment needs."}}}