# Setting Up Sanity.io for CodeSafir

This guide will walk you through setting up Sanity.io as a headless CMS for the CodeSafir website.

## Step 1: Create a Sanity.io Account

1. Go to [sanity.io](https://www.sanity.io/) and sign up for a free account
2. The free tier includes:
   - 10,000 documents
   - 10,000 API requests per month
   - 5GB of media storage
   - 2 datasets
   - 3 users

## Step 2: Create a New Project

1. After signing in, create a new project
2. Choose the "Blog" starter template (we'll customize it later)
3. Name your project "CodeSafir CMS"
4. Choose the "Clean project with no predefined schemas" option

## Step 3: Install Sanity CLI

```bash
npm install -g @sanity/cli
# or
yarn global add @sanity/cli
```

## Step 4: Initialize the Studio in Your Project

```bash
# Navigate to the sanity-studio directory
cd sanity-studio

# Login to your Sanity account
sanity login

# Initialize the studio with your project ID
sanity init --project <your-project-id> --dataset production
```

## Step 5: Update Configuration

1. Replace the placeholder project ID in the following files:
   - `sanity.config.ts`
   - `src/lib/sanity.ts`

2. The project ID can be found in your Sanity project settings

## Step 6: Start the Studio

```bash
cd sanity-studio
npm run dev
# or
yarn dev
```

This will start the Sanity Studio at http://localhost:3333

## Step 7: Create Content Types

The schema files have already been created in the `schemas` directory:
- `portfolio.ts`: For portfolio projects
- `blogPost.ts`: For blog articles
- `author.ts`: For content authors
- `category.ts`: For content categories

## Step 8: Add Content

1. Use the Sanity Studio to add:
   - Categories
   - Authors
   - Portfolio projects
   - Blog posts

2. Start by adding the existing content from:
   - `src/data/portfolio.ts`
   - `src/data/blog-posts.ts`

## Step 9: Deploy the Studio

```bash
cd sanity-studio
npm run deploy
# or
yarn deploy
```

This will deploy your studio to a Sanity-hosted URL like `https://codesafir.sanity.studio/`

## Step 10: CORS Configuration

1. Go to your Sanity project settings
2. Navigate to the API section
3. Add your website URL to the CORS Origins list (e.g., `http://localhost:8080`, `https://codesafir.com`)

## Step 11: Deploy Your Website

After setting up Sanity and adding your content, deploy your website as usual.

## Additional Resources

- [Sanity Documentation](https://www.sanity.io/docs)
- [Sanity React Components](https://www.sanity.io/docs/react-components)
- [Sanity Query Language (GROQ)](https://www.sanity.io/docs/groq)
