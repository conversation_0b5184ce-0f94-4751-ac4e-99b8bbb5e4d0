
import { useState, useEffect, useRef } from "react";
import { Award, Clock, Code2, Users, Briefcase, Globe } from "lucide-react";
import { useTranslation } from "react-i18next";
import { useLanguage } from "@/i18n/LanguageProvider";
import { cn } from "@/lib/utils";

const stats = [
  {
    id: "projects",
    icon: Briefcase,
    value: 200,
    formattedValue: "200+",
    label: "Projects Completed",
  },
  {
    id: "clients",
    icon: Users,
    value: 100,
    formattedValue: "100+",
    label: "Happy Clients",
  },
  {
    id: "satisfaction",
    icon: Award,
    value: 98,
    formattedValue: "98%",
    label: "Client Satisfaction",
  },
  {
    id: "support",
    icon: Clock,
    value: 24,
    formattedValue: "24/7",
    label: "Support Available",
  },
];

const Counter = ({ value, formattedValue }: { value: number; formattedValue: string }) => {
  const [count, setCount] = useState(0);
  const countRef = useRef<HTMLDivElement>(null);
  const animationRef = useRef<number>();
  const startTimeRef = useRef<number>();

  useEffect(() => {
    const observer = new IntersectionObserver(
      (entries) => {
        if (entries[0].isIntersecting) {
          const animate = (timestamp: number) => {
            if (!startTimeRef.current) startTimeRef.current = timestamp;
            const elapsed = timestamp - startTimeRef.current;
            const duration = 2000;
            const progress = Math.min(elapsed / duration, 1);

            // Use easeOutExpo for a nice deceleration
            const easeOutProgress = 1 - Math.pow(1 - progress, 3);
            setCount(Math.floor(easeOutProgress * value));

            if (progress < 1) {
              animationRef.current = requestAnimationFrame(animate);
            }
          };

          animationRef.current = requestAnimationFrame(animate);
          observer.disconnect();
        }
      },
      { threshold: 0.1 }
    );

    if (countRef.current) {
      observer.observe(countRef.current);
    }

    return () => {
      observer.disconnect();
      if (animationRef.current) {
        cancelAnimationFrame(animationRef.current);
      }
    };
  }, [value]);

  return (
    <div ref={countRef} className="text-3xl md:text-4xl font-bold text-gray-900 mb-2 h-12">
      {value === 24 ? "24/7" : formattedValue}
    </div>
  );
};

const Stats = () => {
  const { t } = useTranslation();
  const { direction } = useLanguage();

  return (
    <section className="py-16 md:py-24 bg-background">
      <div className="container">
        <div className="max-w-3xl mx-auto text-center mb-16">
          <span className="inline-block text-sm font-medium text-primary bg-primary/10 rounded-full px-3 py-1 mb-4">
            {t('stats.subtitle', 'By the numbers')}
          </span>
          <h2 className="text-3xl md:text-4xl font-bold mb-6 text-foreground">
            {t('stats.title', 'Proven Track Record of Success')}
          </h2>
          <p className="text-muted-foreground mb-6">
            {t('stats.description', 'Our web development expertise is reflected in our impressive metrics and the success of our clients across various industries.')}
          </p>
        </div>

        <div className="grid grid-cols-2 md:grid-cols-4 gap-8 md:gap-12">
          {stats.map((stat, index) => {
            const Icon = stat.icon;
            return (
              <div
                key={stat.id}
                className="flex flex-col items-center justify-center p-6 rounded-xl bg-gradient-to-b from-card to-muted/50 border border-border
                hover:border-primary/20 hover:shadow-xl transition-all duration-300 group relative overflow-hidden
                before:content-[''] before:absolute before:inset-0 before:bg-gradient-to-r before:from-primary/10 before:via-transparent before:to-primary/10 before:opacity-0 before:group-hover:opacity-100 before:transition-opacity before:duration-500"
                style={{
                  animationDelay: `${index * 150}ms`,
                  transform: "perspective(1000px) rotateX(0deg)",
                  transition: "transform 0.3s ease"
                }}
                onMouseEnter={(e) => {
                  e.currentTarget.style.transform = "perspective(1000px) rotateX(5deg) scale(1.02)";
                }}
                onMouseLeave={(e) => {
                  e.currentTarget.style.transform = "perspective(1000px) rotateX(0deg)";
                }}
              >
                <div className="mb-5 p-4 bg-card rounded-full shadow-md relative z-10 group-hover:shadow-primary/20 transition-shadow">
                  <div className="bg-primary/10 p-3 rounded-full group-hover:bg-primary/20 transition-colors">
                    <Icon className="h-7 w-7 text-primary" />
                  </div>
                </div>
                <Counter value={stat.value} formattedValue={stat.formattedValue} />
                <div className="text-sm font-medium uppercase tracking-wider text-muted-foreground group-hover:text-primary transition-colors relative z-10">
                  {t(`stats.${stat.id}.label`, stat.label)}
                </div>
                <div className={cn(
                  "absolute -bottom-2 w-20 h-20 bg-primary/10 rounded-full opacity-0 group-hover:opacity-20 transition-opacity duration-300 z-0",
                  direction === 'rtl' ? '-left-2' : '-right-2'
                )}></div>
              </div>
            );
          })}
        </div>
      </div>
    </section>
  );
};

export default Stats;
