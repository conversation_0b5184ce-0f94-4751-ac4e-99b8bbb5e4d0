
import { Github, Twitter, Linkedin, Mail, MapPin, Instagram, Facebook, MessageCircle } from "lucide-react";
import { Link } from "react-router-dom";
import { useTranslation } from "react-i18next";
import { CodeSafirLogo } from "@/components/CodeSafirLogo";
import { useLanguage } from "@/i18n/LanguageProvider";
import { cn } from "@/lib/utils";
import React from "react";

// Reusable footer section component
interface FooterSectionProps {
  title: string;
  className?: string;
  children: React.ReactNode;
}

const FooterSection: React.FC<FooterSectionProps> = ({ title, className, children }) => {
  return (
    <div className={className}>
      <h3 className="font-semibold text-sm uppercase text-foreground mb-5 tracking-wider">{title}</h3>
      {children}
    </div>
  );
};

// Logo and social links section
const LogoSection = () => {
  const { t } = useTranslation();
  const { direction } = useLanguage();

  const socialLinks = [
    { icon: Github, label: "GitHub", href: "https://github.com" },
    { icon: Twitter, label: "Twitter", href: "https://twitter.com" },
    { icon: Linkedin, label: "LinkedIn", href: "https://linkedin.com" },
    { icon: Instagram, label: "Instagram", href: "https://instagram.com" },
    { icon: Facebook, label: "Facebook", href: "https://facebook.com" }
  ];

  return (
    <div className="lg:col-span-5">
      <Link to="/" className="inline-block mb-6">
        <CodeSafirLogo className="h-10 w-auto" />
      </Link>
      <p className="text-muted-foreground mb-6 max-w-md">
        {t('home.hero.subtitle')}
      </p>

      {/* Social links */}
      <div className="flex flex-wrap gap-3">
        {socialLinks.map((social) => {
          const Icon = social.icon;
          return (
            <a
              key={social.label}
              href={social.href}
              target="_blank"
              rel="noopener noreferrer"
              className="text-muted-foreground hover:text-primary transition-colors p-2.5 rounded-full hover:bg-muted/80 border border-transparent hover:border-border/50"
              aria-label={social.label}
            >
              <Icon size={18} />
            </a>
          );
        })}
      </div>
    </div>
  );
};

// Services section
const ServicesSection = () => {
  const { t } = useTranslation();
  const { direction } = useLanguage();

  const services = [
    { title: t('features.custom-web-development.title'), path: "/services/custom-web-development" },
    { title: t('features.responsive-design.title'), path: "/services/responsive-design" },
    { title: t('features.ecommerce-solutions.title'), path: "/services/ecommerce-solutions" },
    { title: t('features.ui-ux-design.title'), path: "/services/ui-ux-design" },
    { title: t('features.cms-development.title'), path: "/services/cms-development" },
    { title: t('features.performance-optimization.title'), path: "/services/performance-optimization" }
  ];

  return (
    <FooterSection title={t('footer.services')} className="">
      <ul className="space-y-3">
        {services.map((item) => (
          <li key={item.title}>
            <Link
              to={item.path}
              className="text-muted-foreground hover:text-primary transition-colors text-sm flex items-center group"
            >
              <span className={cn(
                "w-1.5 h-1.5 rounded-full bg-secondary/70 inline-block flex-shrink-0 group-hover:bg-primary",
                direction === 'rtl' ? 'ml-2' : 'mr-2'
              )}></span>
              {item.title}
            </Link>
          </li>
        ))}
      </ul>
    </FooterSection>
  );
};

// Company section
const CompanySection = () => {
  const { t } = useTranslation();
  const { direction } = useLanguage();

  const companyLinks = [
    { title: t('about.hero.title'), path: "/about" },
    { title: t('portfolio.hero.title'), path: "/portfolio" },
    { title: t('about.careers.title'), path: "/about" },
    { title: t('contact.hero.title'), path: "/contact" }
  ];

  return (
    <FooterSection title={t('footer.company')} className="">
      <ul className="space-y-3">
        {companyLinks.map((item) => (
          <li key={item.title}>
            <Link
              to={item.path}
              className="text-muted-foreground hover:text-primary transition-colors text-sm group flex items-center"
            >
              <span className={cn(
                "w-0 group-hover:w-1.5 h-1.5 rounded-full bg-primary opacity-0 group-hover:opacity-100 transition-all duration-300",
                direction === 'rtl' ? 'ml-0 group-hover:ml-2' : 'mr-0 group-hover:mr-2'
              )}></span>
              {item.title}
            </Link>
          </li>
        ))}
      </ul>
    </FooterSection>
  );
};

// Contact section
const ContactSection = () => {
  const { t } = useTranslation();
  const { direction } = useLanguage();

  const contactInfo = [
    { icon: Mail, text: "<EMAIL>", href: "mailto:<EMAIL>" },
    {
      icon: MessageCircle,
      text: t('common.whatsappChat'),
      href: "https://wa.me/201064149151?text=" + encodeURIComponent(t('common.whatsappMessage'))
    },
    { icon: MapPin, text: "Alexandria, Egypt", href: "https://maps.google.com/?q=Alexandria,Egypt" }
  ];

  return (
    <FooterSection title={t('footer.contact')} className="">
      <ul className="space-y-4">
        {contactInfo.map((item) => {
          const Icon = item.icon;
          return (
            <li key={item.text}>
              <a
                href={item.href}
                className={cn(
                  "flex items-center text-muted-foreground hover:text-primary transition-colors group",
                  direction === 'rtl' ? 'space-x-reverse' : ''
                )}
                target={item.icon === MapPin ? "_blank" : undefined}
                rel={item.icon === MapPin ? "noopener noreferrer" : undefined}
              >
                <span className={cn(
                  "flex items-center justify-center rounded-full bg-muted/80 p-1.5 group-hover:bg-primary/10",
                  direction === 'rtl' ? 'ml-3' : 'mr-3'
                )}>
                  <Icon size={14} className="text-muted-foreground group-hover:text-primary" />
                </span>
                <span className="text-sm">{item.text}</span>
              </a>
            </li>
          );
        })}
      </ul>
    </FooterSection>
  );
};

// Copyright and legal section
const CopyrightSection = () => {
  const { t } = useTranslation();
  const { direction } = useLanguage();

  return (
    <div className="pt-8 mt-10 border-t border-border flex flex-col md:flex-row justify-between items-center">
      <div className="flex flex-col items-center md:items-start space-y-2">
        <p className="text-sm text-muted-foreground">
          &copy; {new Date().getFullYear()} CodeSafir. {t('footer.copyright')}
        </p>
        <p className="text-sm text-muted-foreground">Made with ❤️</p>
      </div>
      <div className={cn(
        "mt-4 md:mt-0 flex",
        direction === 'rtl' ? 'space-x-reverse space-x-6' : 'space-x-6'
      )}>
        <Link to="/privacy" className="text-sm text-muted-foreground hover:text-primary transition-colors">
          {t('footer.legal.privacy')}
        </Link>
        <Link to="/terms" className="text-sm text-muted-foreground hover:text-primary transition-colors">
          {t('footer.legal.terms')}
        </Link>
      </div>
    </div>
  );
};

// Main Footer component
const Footer = () => {
  return (
    <footer className="bg-muted/50 border-t border-border relative overflow-hidden">
      {/* Background decorations */}
      <div className="absolute bottom-0 left-0 w-72 h-72 bg-primary/5 rounded-full blur-3xl -z-10 opacity-70"></div>
      <div className="absolute top-1/3 right-0 w-96 h-96 bg-secondary/5 rounded-full blur-3xl -z-10 opacity-70"></div>
      <div className="absolute top-1/2 left-1/3 w-64 h-64 bg-accent/5 rounded-full blur-3xl -z-10 opacity-50"></div>

      <div className="container py-12 md:py-16">
        <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-12 gap-8 md:gap-10">
          <LogoSection />
          <div className="lg:col-span-7 grid grid-cols-1 sm:grid-cols-3 gap-8">
            <ServicesSection />
            <CompanySection />
            <ContactSection />
          </div>
        </div>

        <CopyrightSection />
      </div>
    </footer>
  );
};

export default Footer;
