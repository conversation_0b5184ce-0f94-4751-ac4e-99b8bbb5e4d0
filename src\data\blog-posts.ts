export interface BlogPost {
  id: string;
  title: string;
  excerpt: string;
  date: string;
  author: string;
  category: string;
  image: string;
  tags: string[];
}

export const blogPosts: BlogPost[] = [
  {
    id: "modern-ecommerce-trends",
    title: "7 Modern E-commerce Trends That Will Define 2023",
    excerpt: "Explore the latest trends in e-commerce development that are shaping the future of online retail and driving customer engagement.",
    date: "2023-06-15",
    author: "<PERSON>",
    category: "E-commerce",
    image: "https://images.unsplash.com/photo-1556742049-0cfed4f6a45d?ixlib=rb-4.0.3&auto=format&fit=crop&w=600&q=80",
    tags: ["E-commerce", "Web Development", "Trends"]
  },
  {
    id: "headless-cms-revolution",
    title: "The Headless CMS Revolution: Why Businesses Are Making the Switch",
    excerpt: "Discover why more businesses are adopting headless CMS solutions and how this architecture is transforming content management.",
    date: "2023-05-22",
    author: "<PERSON>",
    category: "CMS",
    image: "https://images.unsplash.com/photo-1507238691740-187a5b1d37b8?ixlib=rb-4.0.3&auto=format&fit=crop&w=600&q=80",
    tags: ["CMS", "Headless", "Content Management"]
  },
  {
    id: "react-vs-vue-2023",
    title: "React vs Vue in 2023: Which Framework Should You Choose?",
    excerpt: "A comprehensive comparison of React and Vue.js, examining performance, ecosystem, and developer experience to help you make the right choice.",
    date: "2023-04-10",
    author: "David Wilson",
    category: "Web Development",
    image: "https://images.unsplash.com/photo-1555066931-4365d14bab8c?ixlib=rb-4.0.3&auto=format&fit=crop&w=600&q=80",
    tags: ["React", "Vue", "JavaScript", "Frameworks"]
  },
  {
    id: "mobile-first-design",
    title: "Mobile-First Design: Best Practices for Modern Web Development",
    excerpt: "Learn why mobile-first design is essential in today's digital landscape and how to implement it effectively in your web projects.",
    date: "2023-03-18",
    author: "Emily Rodriguez",
    category: "UI/UX",
    image: "https://images.unsplash.com/photo-1512941937669-90a1b58e7e9c?ixlib=rb-4.0.3&auto=format&fit=crop&w=600&q=80",
    tags: ["Mobile", "Responsive Design", "UI/UX"]
  },
  {
    id: "nextjs-vs-gatsby",
    title: "Next.js vs Gatsby: Choosing the Right React Framework",
    excerpt: "A detailed comparison of Next.js and Gatsby, exploring their strengths, weaknesses, and ideal use cases for different projects.",
    date: "2023-02-25",
    author: "Alex Thompson",
    category: "Web Development",
    image: "https://images.unsplash.com/photo-1517694712202-14dd9538aa97?ixlib=rb-4.0.3&auto=format&fit=crop&w=600&q=80",
    tags: ["Next.js", "Gatsby", "React", "SSG"]
  },
  {
    id: "ai-web-development",
    title: "How AI is Transforming Web Development in 2023",
    excerpt: "Explore the innovative ways artificial intelligence is being integrated into web development workflows and enhancing user experiences.",
    date: "2023-01-30",
    author: "Sophia Lee",
    category: "Technology",
    image: "https://images.unsplash.com/photo-1677442135136-760c813a743d?ixlib=rb-4.0.3&auto=format&fit=crop&w=600&q=80",
    tags: ["AI", "Machine Learning", "Innovation"]
  }
];
