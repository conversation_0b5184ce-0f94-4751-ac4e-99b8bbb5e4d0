# Email Setup Guide for CodeSafir Contact Form

The contact form is currently configured to send emails to `<EMAIL>` using a fallback mailto approach. For production use, you should integrate with a proper email service.

## Current Setup

- **Recipient**: <EMAIL>
- **Method**: mailto fallback (opens user's email client)
- **Form Fields**: Name, Email, Company (optional), Message

## Recommended Email Services

### Option 1: EmailJS (Recommended)

EmailJS allows sending emails directly from the frontend without a backend server.

1. **Install EmailJS**:
   ```bash
   npm install emailjs-com
   ```

2. **Setup EmailJS Account**:
   - Go to [emailjs.com](https://www.emailjs.com/)
   - Create an account and get your Service ID, Template ID, and User ID
   - Create an email template with these variables:
     - `{{to_email}}` - <EMAIL>
     - `{{from_name}}` - sender's name
     - `{{from_email}}` - sender's email
     - `{{company}}` - sender's company
     - `{{message}}` - message content

3. **Update the code**:
   - Edit `src/lib/email.ts`
   - Uncomment the EmailJS section in `sendEmailViaEmailJS`
   - Replace placeholders with your actual IDs
   - Uncomment the EmailJS call in `sendContactEmail`

### Option 2: Formspree

Formspree is a simple form backend service.

1. **Setup Formspree Account**:
   - Go to [formspree.io](https://formspree.io/)
   - Create an account and get your form endpoint
   - Configure the form to <NAME_EMAIL>

2. **Update the code**:
   - Edit `src/lib/email.ts`
   - Replace `YOUR_FORM_ID` with your actual Formspree form ID
   - Uncomment the Formspree call in `sendContactEmail`

### Option 3: Custom Backend

For more control, create your own backend API:

1. **Create API endpoint** (e.g., `/api/contact`)
2. **Use a service like**:
   - SendGrid
   - Mailgun
   - AWS SES
   - Nodemailer with SMTP

3. **Update the contact form** to call your API

## Environment Variables

For any email service, create a `.env.local` file:

```env
# EmailJS
NEXT_PUBLIC_EMAILJS_SERVICE_ID=your_service_id
NEXT_PUBLIC_EMAILJS_TEMPLATE_ID=your_template_id
NEXT_PUBLIC_EMAILJS_USER_ID=your_user_id

# Formspree
NEXT_PUBLIC_FORMSPREE_FORM_ID=your_form_id

# Custom API
NEXT_PUBLIC_CONTACT_API_URL=your_api_endpoint
```

## Testing

1. Fill out the contact form
2. Check that emails are <NAME_EMAIL>
3. Verify all form fields are included in the email
4. Test error handling for failed submissions

## Current Fallback Behavior

Until you set up a proper email service, the form will:
1. Open the user's default email client
2. Pre-fill the recipient (<EMAIL>)
3. Pre-fill the subject and message body
4. Ask the user to send the email manually

This ensures no contact form submissions are lost while you set up the proper email integration.
