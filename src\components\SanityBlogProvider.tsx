import { createContext, useContext, useState, useEffect, ReactNode, Suspense } from 'react';
import { Loader2 } from 'lucide-react';
import { getBlogPosts } from '@/lib/sanity';
import { BlogPost } from '@/data/blog-posts';
import { LoadingState } from '@/components/ui/loading-state';
import { ErrorBoundary } from '@/components/ui/error-boundary';

interface SanityBlogContextType {
  blogPosts: BlogPost[];
  loading: boolean;
  error: Error | null;
}

const SanityBlogContext = createContext<SanityBlogContextType>({
  blogPosts: [],
  loading: false,
  error: null,
});

export const useSanityBlog = () => useContext(SanityBlogContext);

interface SanityBlogProviderProps {
  children: ReactNode;
  fallbackData?: BlogPost[]; // Local data to use while loading
}

export function SanityBlogProvider({
  children,
  fallbackData = []
}: SanityBlogProviderProps) {
  const [blogPosts, setBlogPosts] = useState<BlogPost[]>(fallbackData);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<Error | null>(null);
  const [isStale, setIsStale] = useState(false);

  useEffect(() => {
    let isMounted = true;

    const fetchBlogPosts = async () => {
      try {
        // Only show loading state if we don't have any data
        if (blogPosts.length === 0) {
          setLoading(true);
        } else {
          // Mark existing data as stale while we fetch new data
          setIsStale(true);
        }

        const posts = await getBlogPosts();

        if (isMounted) {
          setBlogPosts(posts);
          setError(null);
        }
      } catch (err) {
        console.error('Error fetching blog posts:', err);
        if (isMounted) {
          setError(err instanceof Error ? err : new Error('Failed to fetch blog posts'));
        }
      } finally {
        if (isMounted) {
          setLoading(false);
          setIsStale(false);
        }
      }
    };

    fetchBlogPosts();

    // Cleanup subscription
    return () => {
      isMounted = false;
    };
  }, []);

  return (
    <ErrorBoundary>
      <SanityBlogContext.Provider value={{ blogPosts, loading, error }}>
        <Suspense fallback={
          <LoadingState
            message="Loading blog posts..."
            size="lg"
          />
        }>
          {loading && blogPosts.length === 0 ? (
            <LoadingState message="Loading blog posts..." size="lg" />
          ) : (
            <>
              {isStale && (
                <div className="fixed bottom-4 right-4 bg-background/80 backdrop-blur-sm p-2 rounded-md shadow-lg border border-border flex items-center gap-2 text-sm text-muted-foreground">
                  <Loader2 className="h-4 w-4 animate-spin" />
                  <span>Updating content...</span>
                </div>
              )}
              {children}
            </>
          )}
        </Suspense>
      </SanityBlogContext.Provider>
    </ErrorBoundary>
  );
}
