import { Quote } from "lucide-react";
import { useLanguage } from "@/i18n/LanguageProvider";
import { cn } from "@/lib/utils";

interface ProjectTestimonialProps {
  quote: string;
  author: string;
  role: string;
  company: string;
  image?: string;
}

export function ProjectTestimonial({
  quote,
  author,
  role,
  company,
  image
}: ProjectTestimonialProps) {
  const { direction } = useLanguage();

  return (
    <div className="bg-card rounded-xl border border-border p-8 relative">
      <div className="absolute top-6 left-6 text-primary/20">
        <Quote className="h-12 w-12" />
      </div>
      
      <div className="relative z-10">
        <blockquote className="text-lg text-foreground italic mb-6 mt-8">
          "{quote}"
        </blockquote>
        
        <div className={cn(
          "flex items-center",
          direction === 'rtl' ? 'space-x-reverse' : ''
        )}>
          {image && (
            <div className={cn(
              "rounded-full overflow-hidden w-12 h-12 border-2 border-primary/20",
              direction === 'rtl' ? 'ml-4' : 'mr-4'
            )}>
              <img 
                src={image} 
                alt={author} 
                className="w-full h-full object-cover"
              />
            </div>
          )}
          
          <div>
            <div className="font-semibold text-foreground">{author}</div>
            <div className="text-sm text-muted-foreground">
              {role}, {company}
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
