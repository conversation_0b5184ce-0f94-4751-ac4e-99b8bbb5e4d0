import { Code, ShoppingCart, Database, Cloud, Laptop, Server, Palette, Gauge } from "lucide-react";
import { Service, CaseStudy, ProcessStep } from "@/types/services";

export const homePageServices: Service[] = [
    {
        id: 'custom-web-development',
        icon: Code,
        title: 'Custom Web Development',
        description: 'Bespoke websites and web applications built with modern frameworks like React, Vue, and Angular to meet your specific business needs.',
        features: [
            'Modern React, Next.js & Vue.js frontends',
            'Node.js, Python & TypeScript backends',
            'REST, GraphQL & tRPC APIs',
            'Single Page Applications (SPAs)',
            'Progressive Web Apps (PWAs)',
            'Serverless architectures',
            'Microservices implementation',
            'API development and integration'
        ]
    },
    {
        id: 'responsive-design',
        icon: Laptop,
        title: 'Responsive Design',
        description: 'Mobile-first websites that look and function perfectly on all devices, from smartphones to desktops, ensuring a seamless user experience.',
        features: [
            'Mobile-first design approach',
            'Cross-browser compatibility',
            'Responsive layouts and grids',
            'Adaptive images and media',
            'Touch-friendly interfaces',
            'Flexible typography systems',
            'Performance optimization for mobile',
            'Device-specific enhancements'
        ]
    },
    {
        id: 'ecommerce-solutions',
        icon: ShoppingCart,
        title: 'E-commerce Solutions',
        description: 'Custom online stores with secure payment gateways, inventory management, and seamless checkout experiences to boost your sales.',
        features: [
            'Custom e-commerce platforms',
            'Shopify theme development & app integration',
            'WooCommerce & Magento solutions',
            'Multi-currency & payment gateway integration',
            'Inventory & order management systems',
            'Product recommendation engines',
            'Customer analytics & reporting dashboards',
            'Mobile commerce optimization'
        ]
    },
    {
        id: 'ui-ux-design',
        icon: Palette,
        title: 'UI/UX Design',
        description: 'User-centered design that combines aesthetics with functionality, creating intuitive interfaces that delight your users.',
        features: [
            'User research and persona development',
            'Information architecture',
            'Wireframing and prototyping',
            'Visual design and branding',
            'Interaction design',
            'Usability testing',
            'Accessibility compliance (WCAG)',
            'Design systems creation'
        ]
    },
    {
        id: 'cms-development',
        icon: Database,
        title: 'CMS Development',
        description: 'Custom content management systems or integration with platforms like WordPress, Strapi, or Contentful for easy content updates.',
        features: [
            'Contentful & Sanity.io implementation',
            'Strapi custom development',
            'Headless WordPress with Next.js',
            'Content modeling & information architecture',
            'API-first content delivery',
            'Multi-channel publishing workflows',
            'Content localization & internationalization',
            'Custom editor interfaces & extensions'
        ]
    },
    {
        id: 'performance-optimization',
        icon: Gauge,
        title: 'Performance Optimization',
        description: 'Speed up your website with advanced optimization techniques, improving load times, user experience, and search engine rankings.',
        features: [
            'Core Web Vitals optimization',
            'Code splitting and lazy loading',
            'Image and asset optimization',
            'Caching strategies implementation',
            'Bundle size reduction',
            'Server-side rendering (SSR)',
            'Static site generation (SSG)',
            'Performance monitoring and analytics'
        ]
    }
];

export const caseStudies: CaseStudy[] = [
    {
        id: 'ecommerce-platform',
        title: 'E-commerce Platform Redesign',
        client: 'Fashion Retailer',
        description: 'Complete redesign and development of an e-commerce platform with 200% increase in conversion rate',
        technologies: ['Next.js', 'TypeScript', 'Tailwind CSS', 'Shopify API'],
        image: 'https://images.unsplash.com/photo-1523381294911-8d3cead13475?ixlib=rb-4.0.3&auto=format&fit=crop&w=800&q=80'
    },
    {
        id: 'saas-dashboard',
        title: 'SaaS Analytics Dashboard',
        client: 'Tech Startup',
        description: 'Custom analytics dashboard with real-time data visualization and reporting capabilities',
        technologies: ['React', 'Node.js', 'Express', 'MongoDB', 'D3.js'],
        image: 'https://images.unsplash.com/photo-1551288049-bebda4e38f71?ixlib=rb-4.0.3&auto=format&fit=crop&w=800&q=80'
    }
];

export const processSteps: ProcessStep[] = [
    {
        id: "discovery",
        number: "01",
        icon: Laptop,
        titleKey: "services.process.discovery.title",
        descriptionKey: "services.process.discovery.description"
    },
    {
        id: "planning",
        number: "02",
        icon: Palette,
        titleKey: "services.process.planning.title",
        descriptionKey: "services.process.planning.description"
    },
    {
        id: "development",
        number: "03",
        icon: Code,
        titleKey: "services.process.development.title",
        descriptionKey: "services.process.development.description"
    },
    {
        id: "deployment",
        number: "04",
        icon: Server,
        titleKey: "services.process.deployment.title",
        descriptionKey: "services.process.deployment.description"
    },
    {
        id: "testing",
        number: "05",
        icon: Cloud,
        titleKey: "services.process.testing.title",
        descriptionKey: "services.process.testing.description"
    },
    {
        id: "maintenance",
        number: "06",
        icon: Gauge,
        titleKey: "services.process.maintenance.title",
        descriptionKey: "services.process.maintenance.description"
    }
];