import { defineConfig } from 'sanity';
import { deskTool } from 'sanity/desk';
import { visionTool } from '@sanity/vision';
import { schemaTypes } from './schemas';
import { i18n } from './i18n';

export default defineConfig({
  name: 'codesafir',
  title: 'CodeSafir CMS',
  projectId: 'your-project-id', // Replace with your Sanity project ID
  dataset: 'production',
  plugins: [
    deskTool(),
    visionTool(),
    i18n,
  ],
  schema: {
    types: schemaTypes,
  },
  document: {
    // For internationalization support
    languages: ['en', 'ar'],
    languageFilter: (language, context) => {
      // Show all languages in the studio
      return true;
    },
  },
});
